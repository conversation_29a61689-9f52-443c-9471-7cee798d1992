
---
### File: internals/prediction/data_validator.go
```go
// Package prediction provides validation and inference functionality for decision tree models.
package prediction

import (
	"fmt"

	"github.com/berrijam/mulberri/internals/utils"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// ValidationError represents errors that occur during prediction data validation
type ValidationError struct {
	Field    string // Field that caused the error
	Value    string // Value that caused the error
	Reason   string // Human-readable error description
	RowIndex int    // Row index where error occurred (-1 if not applicable)
}

func (e *ValidationError) Error() string {
	if e.RowIndex >= 0 {
		return fmt.Sprintf("validation failed for field '%s' at row %d with value '%s': %s",
			e.Field, e.RowIndex, e.Value, e.Reason)
	}
	return fmt.Sprintf("validation failed for field '%s' with value '%s': %s",
		e.<PERSON>, e.Value, e.Reason)
}

// ValidationResult contains the results of validation
type ValidationResult struct {
	ValidRecords   []utils.PredictionRecord // Records that passed validation
	InvalidRecords []InvalidRecord          // Records that failed validation
	Errors         []ValidationError        // All validation errors encountered
}

// InvalidRecord represents a record that failed validation
type InvalidRecord struct {
	Record utils.PredictionRecord // The original record
	Errors []ValidationError      // Validation errors for this record
}

// ValidatePredictionRecords validates prediction records against model constraints
//
// Parameters:
// - records: Prediction records to validate
// - model: Trained decision tree model containing feature constraints
//
// Returns:
// - ValidationResult: Contains valid records, invalid records, and errors
func ValidatePredictionRecords(records []utils.PredictionRecord, model *models.DecisionTree) (*ValidationResult, error) {
	if model == nil {
		return nil, &ValidationError{
			Field:  "model",
			Value:  "nil",
			Reason: "model cannot be nil",
		}
	}

	if len(model.Features) == 0 {
		return nil, &ValidationError{
			Field:  "model_features",
			Value:  "empty",
			Reason: "model must have features defined",
		}
	}

	if len(records) == 0 {
		return &ValidationResult{
			ValidRecords:   make([]utils.PredictionRecord, 0),
			InvalidRecords: make([]InvalidRecord, 0),
			Errors:         make([]ValidationError, 0),
		}, nil
	}

	result := &ValidationResult{
		ValidRecords:   make([]utils.PredictionRecord, 0, len(records)),
		InvalidRecords: make([]InvalidRecord, 0),
		Errors:         make([]ValidationError, 0),
	}

	for i, record := range records {
		recordErrors := validateSingleRecord(record, model, i)

		if len(recordErrors) == 0 {
			// Record is valid
			result.ValidRecords = append(result.ValidRecords, record)
		} else {
			// Record has validation errors
			result.InvalidRecords = append(result.InvalidRecords, InvalidRecord{
				Record: record,
				Errors: recordErrors,
			})
			result.Errors = append(result.Errors, recordErrors...)
		}
	}

	return result, nil
}

// validateSingleRecord validates a single prediction record
func validateSingleRecord(record utils.PredictionRecord, model *models.DecisionTree, rowIndex int) []ValidationError {
	var errors []ValidationError
	// Remove target column if present
	targetCol := model.TargetColumn
	if _, hasTarget := record.Features[targetCol]; hasTarget {
		delete(record.Features, targetCol)
		// Optionally, add a warning error:
		logger.Debug("removed target value from the record")
	}

	// Check for required features
	for featureName, feature := range model.Features {
		value, exists := record.Features[featureName]

		if !exists {
			errors = append(errors, ValidationError{
				Field:  featureName,
				Value:  "missing",
				Reason: "required feature not found in record",
				RowIndex: rowIndex,
			})
			continue
		}

		// Skip validation for nil values (missing data)
		if value == nil {
			continue
		}

		// Validate based on feature type
		switch feature.Type {
		case models.NumericFeature:
			if validationErr := validateNumericFeature(featureName, value, rowIndex); validationErr != nil {
				errors = append(errors, *validationErr)
			}
		case models.CategoricalFeature:
			if validationErr := validateCategoricalFeature(featureName, value, rowIndex); validationErr != nil {
				errors = append(errors, *validationErr)
			}

		}
	}

	return errors
}

// validateNumericFeature validates a numeric feature value
func validateNumericFeature(featureName string, value interface{}, rowIndex int) *ValidationError {
	// Check if value is numeric
	_, ok := value.(float64)
	if !ok {
		return &ValidationError{
			Field:  featureName,
			Value:  fmt.Sprintf("%v", value),
			Reason: "expected numeric value but got non-numeric type",
			RowIndex: rowIndex,
		}
	}

	return nil
}

// validateCategoricalFeature validates a categorical feature value
func validateCategoricalFeature(featureName string, value interface{}, rowIndex int) *ValidationError {
	// Check if value is string
	_, ok := value.(string)
	if !ok {
		return &ValidationError{
			Field:  featureName,
			Value:  fmt.Sprintf("%v", value),
			Reason: "expected string value but got non-string type",
			RowIndex: rowIndex,
		}
	}

	return nil
}

// ValidateAndFilterPredictionRecords validates records and returns only the valid ones
// This is a convenience function for when you only want valid records
func ValidateAndFilterPredictionRecords(records []utils.PredictionRecord, model *models.DecisionTree) ([]utils.PredictionRecord, error) {
	result, err := ValidatePredictionRecords(records, model)
	if err != nil {
		return nil, err
	}

	return result.ValidRecords, nil
}
```

---
### File: internals/prediction/tree_traversal.go
```go
// Package prediction contains tree traversal and prediction logic.
package prediction

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/berrijam/mulberri/pkg/models"
)

// TraversalResult contains the result of tree traversal
type TraversalResult struct {
	Prediction        interface{}             `json:"prediction"`         // Final prediction value
	ClassDistribution map[interface{}]int     `json:"class_distribution"` // Class distribution at leaf
	Confidence        float64                 `json:"confidence"`         // Confidence score (0-1)
	Path              []string                `json:"path"`               // Path taken through tree
	RulePath          string                  `json:"rule"`               // Decision rule in conjunction format
	LeafNode          *models.TreeNode        `json:"-"`                  // Reference to final leaf node
	Probabilities     map[interface{}]float64 `json:"probabilities"`      // Normalized probabilities
}

// TraversalMetrics contains performance metrics for traversal
type TraversalMetrics struct {
	NodesVisited    int           `json:"nodes_visited"`
	MaxDepthReached int           `json:"max_depth_reached"`
	TraversalTime   time.Duration `json:"traversal_time"`
	Strategy        string        `json:"strategy"` // "recursive" or "iterative"
}

// TraversalStrategy defines the traversal approach
type TraversalStrategy string

const (
	RecursiveTraversal TraversalStrategy = "recursive"
	IterativeTraversal TraversalStrategy = "iterative"
	AutoStrategy       TraversalStrategy = "auto"
)

// MissingValueStrategy defines how to handle missing values during traversal
type MissingValueStrategy string

const (
	// UseMajorityClass sends missing values to the most common branch
	UseMajorityClass MissingValueStrategy = "majority_class"
	// UseDefaultPrediction returns the prediction from current node when value is missing
	UseDefaultPrediction MissingValueStrategy = "default_prediction"
	// FailOnMissing returns an error when a missing value is encountered
	FailOnMissing MissingValueStrategy = "fail_on_missing"
)

// TraversalOptions configures tree traversal behavior
type TraversalOptions struct {
	MissingValueStrategy MissingValueStrategy `json:"missing_value_strategy"`
	IncludePath          bool                 `json:"include_path"`
	MaxDepth             int                  `json:"max_depth"` // Prevent infinite loops
	Strategy             TraversalStrategy    `json:"strategy"`  // Traversal strategy
	CollectMetrics       bool                 `json:"collect_metrics"`
}

const (
	RECURSIVE_THRESHOLD = 50 // Switch to iterative for trees deeper than this
	DEFAULT_MAX_DEPTH   = 1000
)

// depthCache provides thread-safe caching of tree depths
var depthCache = struct {
	mu    sync.RWMutex
	cache map[*models.DecisionTree]int
}{
	cache: make(map[*models.DecisionTree]int),
}

// DefaultTraversalOptions returns sensible default options
func DefaultTraversalOptions() TraversalOptions {
	return TraversalOptions{
		MissingValueStrategy: UseMajorityClass,
		IncludePath:          false,
		MaxDepth:             DEFAULT_MAX_DEPTH,
		Strategy:             AutoStrategy,
		CollectMetrics:       false,
	}
}

// ValidateInputRecord validates the input record against tree features
func ValidateInputRecord(record map[string]interface{}, tree *models.DecisionTree) error {
	if record == nil {
		return &models.ModelError{
			Op:     "validate_input_record",
			Field:  "record",
			Reason: "record cannot be nil",
		}
	}

	for featureName, feature := range tree.Features {
		value, exists := record[featureName]
		if !exists {
			continue // Will be handled by missing value strategy
		}

		if err := validateFeatureValue(value, feature); err != nil {
			return &models.ModelError{
				Op:     "validate_input_record",
				Field:  featureName,
				Value:  fmt.Sprintf("%v", value),
				Reason: fmt.Sprintf("invalid feature value: %v", err),
				Err:    err,
			}
		}
	}

	return nil
}

// validateFeatureValue validates a single feature value
func validateFeatureValue(value interface{}, feature *models.Feature) error {
	if value == nil {
		return nil // Handled by missing value strategy
	}

	switch feature.Type {
	case models.NumericFeature, models.DateFeature:
		if _, err := convertToFloat64(value); err != nil {
			return fmt.Errorf("cannot convert to numeric: %v", err)
		}
	case models.CategoricalFeature:
		// Any value can be converted to string for categorical
		return nil
	default:
		return fmt.Errorf("unsupported feature type: %v", feature.Type)
	}

	return nil
}

// isSingleNodeTree checks if the tree has only one node
func isSingleNodeTree(tree *models.DecisionTree) bool {
	return tree.Root != nil && tree.Root.IsLeaf()
}

// getTreeDepth gets the cached depth or computes and caches it
func getTreeDepth(tree *models.DecisionTree) int {
	if tree == nil || tree.Root == nil {
		return 0
	}

	// Try to get from cache first (read lock)
	depthCache.mu.RLock()
	if depth, exists := depthCache.cache[tree]; exists {
		depthCache.mu.RUnlock()
		return depth
	}
	depthCache.mu.RUnlock()

	// Not in cache, compute it (write lock)
	depthCache.mu.Lock()
	defer depthCache.mu.Unlock()

	// Double-check in case another goroutine computed it while we waited
	if depth, exists := depthCache.cache[tree]; exists {
		return depth
	}

	// Compute and cache the depth
	depth := estimateTreeDepth(tree.Root)
	depthCache.cache[tree] = depth
	return depth
}

// ClearDepthCache clears the depth cache (useful for testing or memory cleanup)
func ClearDepthCache() {
	depthCache.mu.Lock()
	defer depthCache.mu.Unlock()
	depthCache.cache = make(map[*models.DecisionTree]int)
}

// estimateTreeDepth estimates the maximum depth of the tree (internal function)
func estimateTreeDepth(node *models.TreeNode) int {
	if node == nil || node.IsLeaf() {
		return 0
	}

	maxDepth := 0

	// Check left and right children
	if node.Left != nil {
		depth := 1 + estimateTreeDepth(node.Left)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	if node.Right != nil {
		depth := 1 + estimateTreeDepth(node.Right)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	// Check categorical children
	for _, child := range node.Categories {
		if child != nil {
			depth := 1 + estimateTreeDepth(child)
			if depth > maxDepth {
				maxDepth = depth
			}
		}
	}

	return maxDepth
}

// chooseTraversalStrategy automatically selects the best traversal strategy
func chooseTraversalStrategy(tree *models.DecisionTree, options TraversalOptions) TraversalStrategy {
	if options.Strategy != AutoStrategy {
		return options.Strategy
	}

	// For single node trees, strategy doesn't matter
	if isSingleNodeTree(tree) {
		return RecursiveTraversal
	}

	// Estimate tree depth using cached value
	estimatedDepth := getTreeDepth(tree)

	if estimatedDepth > RECURSIVE_THRESHOLD {
		return IterativeTraversal
	}

	return RecursiveTraversal
}

// normalizeClassDistribution converts class counts to probabilities
func normalizeClassDistribution(classDistribution map[interface{}]int) map[interface{}]float64 {
	probabilities := make(map[interface{}]float64)
	total := 0

	// Calculate total count
	for _, count := range classDistribution {
		total += count
	}

	if total == 0 {
		return probabilities
	}

	// Convert to probabilities
	for class, count := range classDistribution {
		probabilities[class] = float64(count) / float64(total)
	}

	return probabilities
}

// buildRule converts path steps to conjunction format
func buildRule(path []string) string {
	if len(path) == 0 {
		return ""
	}

	var conditions []string
	var leafPrediction string

	for _, step := range path {
		if strings.HasPrefix(step, "LEAF[") {
			leafPrediction = step
		} else if !strings.HasPrefix(step, "MISSING[") {
			conditions = append(conditions, step)
		}
	}

	if len(conditions) == 0 {
		return leafPrediction
	}

	rule := strings.Join(conditions, " & ")
	if leafPrediction != "" {
		rule += " --> " + leafPrediction
	}

	return "[" + rule + "]"
}

// TraverseTree traverses a decision tree to make a prediction for the given input record
func TraverseTree(tree *models.DecisionTree, record map[string]interface{}, options TraversalOptions) (*TraversalResult, error) {
	if tree == nil {
		return nil, &models.ModelError{
			Op:     "traverse_tree",
			Field:  "tree",
			Reason: "tree cannot be nil",
		}
	}

	if tree.Root == nil {
		return nil, &models.ModelError{
			Op:     "traverse_tree",
			Field:  "root",
			Reason: "tree has no root node (tree not trained)",
		}
	}

	if record == nil {
		return nil, &models.ModelError{
			Op:     "traverse_tree",
			Field:  "record",
			Reason: "input record cannot be nil",
		}
	}

	// Validate input record
	if err := ValidateInputRecord(record, tree); err != nil {
		return nil, err
	}

	// Choose traversal strategy
	strategy := chooseTraversalStrategy(tree, options)

	var startTime time.Time
	if options.CollectMetrics {
		startTime = time.Now()
	}

	// Initialize traversal state
	var path []string
	if options.IncludePath {
		path = make([]string, 0, getTreeDepth(tree))
	}

	var leafNode *models.TreeNode
	var traversalPath []string
	var metrics *TraversalMetrics
	var err error

	// Execute traversal based on strategy
	switch strategy {
	case IterativeTraversal:
		leafNode, traversalPath, metrics, err = traverseNodeIterative(tree.Root, record, tree.Features, path, options)
	default: // RecursiveTraversal
		if options.CollectMetrics {
			metrics = &TraversalMetrics{
				Strategy: string(RecursiveTraversal),
			}
			leafNode, traversalPath, metrics, err = traverseNodeRecursive(tree.Root, record, tree.Features, path, options, 0, metrics)
		} else {
			leafNode, traversalPath, _, err = traverseNodeRecursive(tree.Root, record, tree.Features, path, options, 0, nil)
		}
	}

	if err != nil {
		return nil, err
	}

	// Complete metrics collection
	if options.CollectMetrics && metrics != nil {
		metrics.TraversalTime = time.Since(startTime)
	}

	// Build result
	result := &TraversalResult{
		Prediction:        leafNode.Prediction,
		ClassDistribution: leafNode.ClassDistribution,
		Confidence:        leafNode.Confidence,
		LeafNode:          leafNode,
		Probabilities:     normalizeClassDistribution(leafNode.ClassDistribution),
	}

	if options.IncludePath {
		result.Path = traversalPath
		result.RulePath = buildRule(traversalPath)
	}

	return result, nil
}

// TraverseTreeWithMetrics traverses a tree and returns detailed performance metrics
func TraverseTreeWithMetrics(tree *models.DecisionTree, record map[string]interface{}, options TraversalOptions) (*TraversalResult, *TraversalMetrics, error) {
	if options.MaxDepth == 0 {
		options.MaxDepth = DEFAULT_MAX_DEPTH
	}
	options.CollectMetrics = true
	result, err := TraverseTree(tree, record, options)

	var metrics *TraversalMetrics
	if err == nil {
		// Metrics would be collected during traversal in a full implementation
		// For now, return basic metrics
		strategy := chooseTraversalStrategy(tree, options)
		metrics = &TraversalMetrics{
			Strategy:        string(strategy),
			NodesVisited:    len(result.Path), // Approximate
			MaxDepthReached: len(result.Path), // Approximate
		}
	}

	return result, metrics, err
}

// TraverseTreeSimple provides a simplified interface for tree traversal with default options
func TraverseTreeSimple(tree *models.DecisionTree, record map[string]interface{}) (*TraversalResult, error) {
	return TraverseTree(tree, record, DefaultTraversalOptions())
}

// traverseNodeIterative implements iterative tree traversal for deep trees
func traverseNodeIterative(root *models.TreeNode, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions) (*models.TreeNode, []string, *TraversalMetrics, error) {
	type stackItem struct {
		node  *models.TreeNode
		depth int
		path  []string
	}

	stack := []stackItem{{node: root, depth: 0, path: path}}
	metrics := &TraversalMetrics{
		Strategy: string(IterativeTraversal),
	}

	for len(stack) > 0 {
		// Pop from stack
		current := stack[len(stack)-1]
		stack = stack[:len(stack)-1]

		metrics.NodesVisited++
		if current.depth > metrics.MaxDepthReached {
			metrics.MaxDepthReached = current.depth
		}

		// Check depth limit
		if current.depth > options.MaxDepth {
			return nil, nil, metrics, &models.ModelError{
				Op:     "traverse_node_iterative",
				Field:  "depth",
				Value:  fmt.Sprintf("%d", current.depth),
				Reason: fmt.Sprintf("traversal depth exceeded limit of %d", options.MaxDepth),
			}
		}

		// Base case: reached a leaf node
		if current.node.IsLeaf() {
			if options.IncludePath {
				current.path = append(current.path, fmt.Sprintf("LEAF[%v]", current.node.Prediction))
			}
			return current.node, current.path, metrics, nil
		}

		// Decision node: determine which child to traverse
		if current.node.Feature == nil {
			return nil, nil, metrics, &models.ModelError{
				Op:     "traverse_node_iterative",
				Field:  "feature",
				Reason: "decision node has no feature",
			}
		}

		// Get the feature value from the input record
		featureValue, exists := record[current.node.Feature.Name]

		// Handle missing values
		if !exists || featureValue == nil {
			result := handleMissingValue(current.node, record, features, current.path, options)
			if result.Error != nil {
				return nil, nil, metrics, result.Error
			}
			if result.IsLeaf {
				return result.NextNode, result.Path, metrics, nil
			}
			stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
			continue
		}

		// Route based on feature type
		var nextNode *models.TreeNode
		var pathSegment string

		switch current.node.Feature.Type {
		case models.NumericFeature, models.DateFeature:
			numericValue, err := convertToFloat64(featureValue)
			if err != nil {
				return nil, nil, metrics, &models.ModelError{
					Op:     "traverse_node_iterative",
					Field:  "feature_value",
					Value:  fmt.Sprintf("%v", featureValue),
					Reason: fmt.Sprintf("cannot convert to numeric value: %v", err),
					Err:    err,
				}
			}

			if math.IsNaN(numericValue) || math.IsInf(numericValue, 0) {
				result := handleMissingValue(current.node, record, features, current.path, options)
				if result.Error != nil {
					return nil, nil, metrics, result.Error
				}
				if result.IsLeaf {
					return result.NextNode, result.Path, metrics, nil
				}
				stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
				continue
			}

			if numericValue <= current.node.Threshold {
				nextNode = current.node.Left
				pathSegment = fmt.Sprintf("%s <= %.6f", current.node.Feature.Name, current.node.Threshold)
			} else {
				nextNode = current.node.Right
				pathSegment = fmt.Sprintf("%s > %.6f", current.node.Feature.Name, current.node.Threshold)
			}

		case models.CategoricalFeature:
			categoryValue := fmt.Sprintf("%v", featureValue)
			var exists bool
			nextNode, exists = current.node.Categories[featureValue]
			if !exists {
				nextNode, exists = current.node.Categories[categoryValue]
			}
			pathSegment = fmt.Sprintf("%s = %s", current.node.Feature.Name, categoryValue)

			if !exists || nextNode == nil {
				result := handleMissingValue(current.node, record, features, current.path, options)
				if result.Error != nil {
					return nil, nil, metrics, result.Error
				}
				if result.IsLeaf {
					return result.NextNode, result.Path, metrics, nil
				}
				stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
				continue
			}

		default:
			return nil, nil, metrics, &models.ModelError{
				Op:     "traverse_node_iterative",
				Field:  "feature_type",
				Value:  string(current.node.Feature.Type),
				Reason: "unsupported feature type",
			}
		}

		// Update path if tracking is enabled
		newPath := current.path
		if options.IncludePath {
			newPath = append(newPath, pathSegment)
		}

		// Handle case where child doesn't exist
		if nextNode == nil {
			result := handleMissingValue(current.node, record, features, newPath, options)
			if result.Error != nil {
				return nil, nil, metrics, result.Error
			}
			if result.IsLeaf {
				return result.NextNode, result.Path, metrics, nil
			}
			stack = append(stack, stackItem{node: result.NextNode, depth: current.depth + 1, path: result.Path})
			continue
		}

		// Push next node to stack
		stack = append(stack, stackItem{node: nextNode, depth: current.depth + 1, path: newPath})
	}

	return nil, nil, metrics, &models.ModelError{
		Op:     "traverse_node_iterative",
		Reason: "traversal completed without reaching leaf node",
	}
}

// MissingValueResult contains the result of missing value handling
type MissingValueResult struct {
	NextNode *models.TreeNode
	Path     []string
	IsLeaf   bool
	Error    error
}

// handleMissingValue handles missing values for both recursive and iterative traversal
func handleMissingValue(node *models.TreeNode, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions) *MissingValueResult {
	result := &MissingValueResult{
		Path: path,
	}

	switch options.MissingValueStrategy {
	case FailOnMissing:
		result.Error = &models.ModelError{
			Op:     "handle_missing_value",
			Field:  "feature_value",
			Value:  node.Feature.Name,
			Reason: "missing value encountered and FailOnMissing strategy is enabled",
		}
		return result

	case UseDefaultPrediction:
		majorityClass := node.GetMajorityClass()
		if majorityClass == nil {
			majorityClass = "unknown"
		}

		if options.IncludePath {
			result.Path = append(result.Path, fmt.Sprintf("MISSING[%s] -> DEFAULT[%v]", node.Feature.Name, majorityClass))
		}

		// Create synthetic leaf node
		result.NextNode = &models.TreeNode{
			Type:              models.LeafNode,
			Prediction:        majorityClass,
			ClassDistribution: node.ClassDistribution,
			Samples:           node.Samples,
			Confidence:        node.Confidence,
			Impurity:          node.Impurity,
		}
		result.IsLeaf = true
		return result

	case UseMajorityClass:
		var bestChild *models.TreeNode
		maxSamples := -1

		// Check left and right children for numeric features
		if node.Left != nil && node.Left.Samples > maxSamples {
			maxSamples = node.Left.Samples
			bestChild = node.Left
		}
		if node.Right != nil && node.Right.Samples > maxSamples {
			maxSamples = node.Right.Samples
			bestChild = node.Right
		}

		// Check categorical children
		for _, child := range node.Categories {
			if child != nil && child.Samples > maxSamples {
				maxSamples = child.Samples
				bestChild = child
			}
		}

		if bestChild == nil {
			// No children available, fallback to UseDefaultPrediction
			fallbackOptions := TraversalOptions{
				MissingValueStrategy: UseDefaultPrediction,
				IncludePath:          options.IncludePath,
				MaxDepth:             options.MaxDepth,
			}
			return handleMissingValue(node, record, features, result.Path, fallbackOptions)
		}

		// Update path if tracking is enabled
		if options.IncludePath {
			result.Path = append(result.Path, fmt.Sprintf("MISSING[%s] -> MAJORITY_BRANCH", node.Feature.Name))
		}

		result.NextNode = bestChild
		result.IsLeaf = bestChild.IsLeaf()
		return result

	default:
		result.Error = &models.ModelError{
			Op:     "handle_missing_value",
			Field:  "missing_value_strategy",
			Value:  string(options.MissingValueStrategy),
			Reason: "unknown missing value strategy",
		}
		return result
	}
}

// traverseNodeRecursive recursively traverses the tree from the given node and collects metrics
func traverseNodeRecursive(node *models.TreeNode, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions, depth int, metrics *TraversalMetrics) (*models.TreeNode, []string, *TraversalMetrics, error) {
	// Update metrics
	if metrics != nil {
		metrics.NodesVisited++
		if depth > metrics.MaxDepthReached {
			metrics.MaxDepthReached = depth
		}
	}

	// Check depth limit to prevent infinite loops
	if depth > options.MaxDepth {
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_node_recursive",
			Field:  "depth",
			Value:  fmt.Sprintf("%d", depth),
			Reason: fmt.Sprintf("traversal depth exceeded limit of %d", options.MaxDepth),
		}
	}

	// Base case: reached a leaf node
	if node.IsLeaf() {
		if options.IncludePath {
			path = append(path, fmt.Sprintf("LEAF[%v]", node.Prediction))
		}
		return node, path, metrics, nil
	}

	// Decision node: determine which child to traverse
	if node.Feature == nil {
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_node_recursive",
			Field:  "feature",
			Reason: "decision node has no feature",
		}
	}

	// Get the feature value from the input record
	featureValue, exists := record[node.Feature.Name]

	// Handle missing values
	if !exists || featureValue == nil {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		// Continue traversal with the selected node
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Route based on feature type
	switch node.Feature.Type {
	case models.NumericFeature, models.DateFeature:
		return traverseNumericFeatureRecursive(node, featureValue, record, features, path, options, depth, metrics)
	case models.CategoricalFeature:
		return traverseCategoricalFeatureRecursive(node, featureValue, record, features, path, options, depth, metrics)
	default:
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_node_recursive",
			Field:  "feature_type",
			Value:  string(node.Feature.Type),
			Reason: "unsupported feature type",
		}
	}
}

// traverseNumericFeatureRecursive handles traversal for numeric and date features
func traverseNumericFeatureRecursive(node *models.TreeNode, featureValue interface{}, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions, depth int, metrics *TraversalMetrics) (*models.TreeNode, []string, *TraversalMetrics, error) {
	// Convert feature value to float64
	numericValue, err := convertToFloat64(featureValue)
	if err != nil {
		return nil, nil, metrics, &models.ModelError{
			Op:     "traverse_numeric_feature_recursive",
			Field:  "feature_value",
			Value:  fmt.Sprintf("%v", featureValue),
			Reason: fmt.Sprintf("cannot convert to numeric value: %v", err),
			Err:    err,
		}
	}

	// Check for NaN or Inf
	if math.IsNaN(numericValue) || math.IsInf(numericValue, 0) {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Determine which child to traverse based on threshold
	var nextNode *models.TreeNode
	var pathSegment string

	if numericValue <= node.Threshold {
		nextNode = node.Left
		pathSegment = fmt.Sprintf("%s <= %.6f", node.Feature.Name, node.Threshold)
	} else {
		nextNode = node.Right
		pathSegment = fmt.Sprintf("%s > %.6f", node.Feature.Name, node.Threshold)
	}

	// Update path if tracking is enabled
	if options.IncludePath {
		path = append(path, pathSegment)
	}

	// Handle case where child doesn't exist
	if nextNode == nil {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Recursively traverse the selected child
	return traverseNodeRecursive(nextNode, record, features, path, options, depth+1, metrics)
}

// traverseCategoricalFeatureRecursive handles traversal for categorical features
func traverseCategoricalFeatureRecursive(node *models.TreeNode, featureValue interface{}, record map[string]interface{}, features map[string]*models.Feature, path []string, options TraversalOptions, depth int, metrics *TraversalMetrics) (*models.TreeNode, []string, *TraversalMetrics, error) {
	// Convert feature value to string for comparison
	categoryValue := fmt.Sprintf("%v", featureValue)

	// Look up the child node for this category
	nextNode, exists := node.Categories[featureValue]
	if !exists {
		// Try string version as well for compatibility
		nextNode, exists = node.Categories[categoryValue]
	}

	// Update path if tracking is enabled
	if options.IncludePath {
		path = append(path, fmt.Sprintf("%s = %s", node.Feature.Name, categoryValue))
	}

	// Handle case where category doesn't exist in tree
	if !exists || nextNode == nil {
		result := handleMissingValue(node, record, features, path, options)
		if result.Error != nil {
			return nil, nil, metrics, result.Error
		}
		if result.IsLeaf {
			return result.NextNode, result.Path, metrics, nil
		}
		return traverseNodeRecursive(result.NextNode, record, features, result.Path, options, depth+1, metrics)
	}

	// Recursively traverse the selected child
	return traverseNodeRecursive(nextNode, record, features, path, options, depth+1, metrics)
}

// convertToFloat64 converts various numeric types to float64
func convertToFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		// Try to parse string as number
		if parsed, err := strconv.ParseFloat(v, 64); err == nil {
			return parsed, nil
		}
		return 0, fmt.Errorf("cannot parse string '%s' as number", v)
	default:
		return 0, fmt.Errorf("unsupported type %T", value)
	}
}

// PredictBatch makes predictions for multiple input records efficiently
func PredictBatch(tree *models.DecisionTree, records []map[string]interface{}, options TraversalOptions) ([]*TraversalResult, error) {
	if tree == nil {
		return nil, &models.ModelError{
			Op:     "predict_batch",
			Field:  "tree",
			Reason: "tree cannot be nil",
		}
	}

	if records == nil {
		return nil, &models.ModelError{
			Op:     "predict_batch",
			Field:  "records",
			Reason: "records cannot be nil",
		}
	}

	results := make([]*TraversalResult, len(records))
	for i, record := range records {
		result, err := TraverseTree(tree, record, options)
		if err != nil {
			return nil, &models.ModelError{
				Op:     "predict_batch",
				Field:  "record",
				Value:  fmt.Sprintf("index_%d", i),
				Reason: fmt.Sprintf("failed to predict record at index %d: %v", i, err),
				Err:    err,
			}
		}
		results[i] = result
	}

	return results, nil
}

// ExtractPredictions extracts just the prediction values from traversal results
func ExtractPredictions(results []*TraversalResult) []interface{} {
	predictions := make([]interface{}, len(results))
	for i, result := range results {
		if result != nil {
			predictions[i] = result.Prediction
		}
	}
	return predictions
}

// AnalyzeConfidenceStats analyzes confidence values from traversal results and computes statistical measures.
// This function does not calculate confidence values - it performs statistical analysis on pre-existing
// confidence values that were retrieved from tree leaf nodes during traversal.
//
// The function computes:
// - Mean, Min, Max confidence values
// - Standard deviation of confidence values
// - Count of low confidence predictions (< 0.5)
// - Count of high confidence predictions (>= 0.8)
//
// Returns statistical measures that can be used for model quality assessment and monitoring.
type ConfidenceStats struct {
	Mean      float64 `json:"mean"`
	Min       float64 `json:"min"`
	Max       float64 `json:"max"`
	StdDev    float64 `json:"std_dev"`
	LowCount  int     `json:"low_count"`  // Count of predictions with confidence < 0.5
	HighCount int     `json:"high_count"` // Count of predictions with confidence >= 0.8
}

// AnalyzeConfidenceStats analyzes confidence values from traversal results and computes statistical measures
func AnalyzeConfidenceStats(results []*TraversalResult) *ConfidenceStats {
	if len(results) == 0 {
		return &ConfidenceStats{}
	}

	stats := &ConfidenceStats{
		Min: 1.0,
		Max: 0.0,
	}

	sum := 0.0
	sumSquares := 0.0

	for _, result := range results {
		if result == nil {
			continue
		}

		confidence := result.Confidence
		sum += confidence
		sumSquares += confidence * confidence

		if confidence < stats.Min {
			stats.Min = confidence
		}
		if confidence > stats.Max {
			stats.Max = confidence
		}

		if confidence < 0.5 {
			stats.LowCount++
		}
		if confidence >= 0.8 {
			stats.HighCount++
		}
	}

	n := float64(len(results))
	stats.Mean = sum / n

	// Calculate standard deviation
	variance := (sumSquares / n) - (stats.Mean * stats.Mean)
	if variance > 0 {
		stats.StdDev = math.Sqrt(variance)
	}

	return stats
}
```

---
### File: internals/training/builder/config_validator.go
```go
package builder

import (
	"fmt"

	"github.com/berrijam/mulberri/pkg/models"
)

// DefaultConfigValidator provides the default implementation of ConfigValidator interface
type DefaultConfigValidator struct {
	config TreeConfiguration
}

// NewDefaultConfigValidator creates a new default config validator
func NewDefaultConfigValidator(config TreeConfiguration) *DefaultConfigValidator {
	return &DefaultConfigValidator{
		config: config,
	}
}

// ValidateConfiguration validates the tree building configuration
func (cv *DefaultConfigValidator) ValidateConfiguration() error {
	// Validate relationship between min samples split and leaf
	if cv.config.GetMinSamplesLeaf() > cv.config.GetMinSamplesSplit() {
		return &BuilderError{
			Op:    "validate_configuration",
			Field: "min_samples_leaf",
			Value: fmt.Sprintf("%d", cv.config.GetMinSamplesLeaf()),
			Reason: fmt.Sprintf("min_samples_leaf (%d) cannot be greater than min_samples_split (%d)",
				cv.config.GetMinSamplesLeaf(), cv.config.GetMinSamplesSplit()),
		}
	}

	// Validate min impurity decrease
	if cv.config.GetMinImpurityDecrease() < 0.0 {
		return &BuilderError{
			Op:     "validate_configuration",
			Field:  "min_impurity_decrease",
			Value:  fmt.Sprintf("%.6f", cv.config.GetMinImpurityDecrease()),
			Reason: "min impurity decrease cannot be negative",
		}
	}

	// Validate target type
	targetType := cv.config.GetTargetType()
	if !isValidTargetType(targetType) {
		return &BuilderError{
			Op:     "validate_configuration",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "invalid target type",
		}
	}

	return nil
}

// ValidateCompatibility validates compatibility between configuration and type
func (cv *DefaultConfigValidator) ValidateCompatibility() error {
	// This is a placeholder for type-specific validation
	// In a real implementation, this would validate that the generic type T
	// is compatible with the configured target type
	return nil
}

// isValidTargetType checks if the target type is valid
func isValidTargetType(targetType models.FeatureType) bool {
	switch targetType {
	case models.NumericFeature, models.CategoricalFeature, models.DateFeature:
		return true
	default:
		return false
	}
}

// ValidateOptionConsistency validates that option combinations are consistent
func (cv *DefaultConfigValidator) ValidateOptionConsistency() error {
	// Check for unrealistic combinations
	if cv.config.GetMaxDepth() > 50 {
		return &BuilderError{
			Op:     "validate_option_consistency",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", cv.config.GetMaxDepth()),
			Reason: "max depth greater than 50 may cause performance issues",
		}
	}

	if cv.config.GetMinSamplesSplit() > 10000 {
		return &BuilderError{
			Op:     "validate_option_consistency",
			Field:  "min_samples_split",
			Value:  fmt.Sprintf("%d", cv.config.GetMinSamplesSplit()),
			Reason: "min samples split greater than 10000 may be too restrictive",
		}
	}

	if cv.config.GetMinImpurityDecrease() > 0.5 {
		return &BuilderError{
			Op:     "validate_option_consistency",
			Field:  "min_impurity_decrease",
			Value:  fmt.Sprintf("%.6f", cv.config.GetMinImpurityDecrease()),
			Reason: "min impurity decrease greater than 0.5 may prevent any splits",
		}
	}

	return nil
}

// GetConfigurationSummary returns a human-readable summary of the configuration
func (cv *DefaultConfigValidator) GetConfigurationSummary() string {
	return fmt.Sprintf(
		"TreeConfig{MaxDepth: %d, MinSamplesSplit: %d, MinSamplesLeaf: %d, MinImpurityDecrease: %.3f, Criterion: %s, TargetType: %s, Logging: %t}",
		cv.config.GetMaxDepth(),
		cv.config.GetMinSamplesSplit(),
		cv.config.GetMinSamplesLeaf(),
		cv.config.GetMinImpurityDecrease(),
		string(cv.config.GetCriterion()),
		string(cv.config.GetTargetType()),
		cv.config.GetEnableLogging(),
	)
}

// ValidateForDataset validates configuration against dataset characteristics
func (cv *DefaultConfigValidator) ValidateForDataset(datasetSize int, featureCount int) error {
	if datasetSize < cv.config.GetMinSamplesSplit() {
		return &BuilderError{
			Op:    "validate_for_dataset",
			Field: "dataset_size",
			Value: fmt.Sprintf("%d", datasetSize),
			Reason: fmt.Sprintf("dataset size (%d) is smaller than min_samples_split (%d)",
				datasetSize, cv.config.GetMinSamplesSplit()),
		}
	}

	if featureCount == 0 {
		return &BuilderError{
			Op:     "validate_for_dataset",
			Field:  "feature_count",
			Value:  "0",
			Reason: "cannot build tree with zero features",
		}
	}

	// Warn about potential overfitting
	if cv.config.GetMinSamplesLeaf() == 1 && datasetSize < 100 {
		// This is just a warning, not an error
		// In a real implementation, you might use a logger here
	}

	return nil
}
```

---
### File: internals/training/builder/errors.go
```go
package builder

import "fmt"

// BuilderError represents errors during tree building operations
type BuilderError struct {
	Op     string // Operation that failed
	Field  string // Field that caused the error (optional)
	Value  string // Value that caused the error (optional)
	Reason string // Human-readable reason
	Err    error  // Underlying error (optional)
}

func (e *BuilderError) Error() string {
	if e.Field != "" && e.Value != "" {
		return fmt.Sprintf("builder %s failed for field '%s' (value: %s): %s", e.Op, e.Field, e.Value, e.Reason)
	}
	if e.Field != "" {
		return fmt.Sprintf("builder %s failed for field '%s': %s", e.Op, e.Field, e.Reason)
	}
	return fmt.Sprintf("builder %s failed: %s", e.Op, e.Reason)
}

func (e *BuilderError) Unwrap() error {
	return e.Err
}
```

---
### File: internals/training/builder/interfaces.go
```go
package builder

import (
	"context"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// NodeBuilder defines the interface for building different types of tree nodes
// This separates node construction concerns from tree building logic
type NodeBuilder[T comparable] interface {
	// BuildLeafNode creates a leaf node from node statistics
	BuildLeafNode(stats *NodeStatistics[T]) (*models.TreeNode, error)

	// BuildDecisionNode creates a decision node from split result and children
	BuildDecisionNode(split *training.SplitResult[T], leftChild, rightChild *models.TreeNode) (*models.TreeNode, error)
}

// TreeConstructor defines the interface for tree construction operations
// This separates high-level tree building from node-level operations
type TreeConstructor[T comparable] interface {
	// BuildTree constructs a complete decision tree from dataset and features
	BuildTree(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*models.DecisionTree, error)

	// ValidateInputs validates the inputs before tree construction
	ValidateInputs(dataset training.Dataset[T], features []*models.Feature) error
}

// StatisticsCalculator defines the interface for calculating node statistics
// This separates statistical calculations from tree building logic
type StatisticsCalculator[T comparable] interface {
	// CalculateNodeStatistics computes statistics for a dataset at a node
	CalculateNodeStatistics(dataset training.Dataset[T]) (*NodeStatistics[T], error)
}

// ConfigValidator defines the interface for configuration validation
// This separates configuration validation concerns
type ConfigValidator interface {
	// ValidateConfiguration validates the tree building configuration
	ValidateConfiguration() error

	// ValidateCompatibility validates compatibility between configuration and type
	ValidateCompatibility() error

	// ValidateForDataset validates configuration against dataset characteristics
	ValidateForDataset(datasetSize int, featureCount int) error
}

// NodeStatistics represents statistical information about a node
// This replaces the internal nodeStatistics struct with a public interface-friendly version
type NodeStatistics[T comparable] struct {
	SampleCount       int
	ClassDistribution map[T]int
	MajorityClass     T
	Confidence        float64
	Impurity          float64
}

// SplittingStrategy defines the interface for different splitting algorithms
// This allows for pluggable splitting strategies in the future
type SplittingStrategy[T comparable] interface {
	// FindBestSplit finds the best split for the given dataset and features
	FindBestSplit(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*training.SplitResult[T], error)
}

// TreeBuilderComponents groups all the components needed for tree building
// This provides a clean way to inject dependencies
type TreeBuilderComponents[T comparable] struct {
	NodeBuilder          NodeBuilder[T]
	StatisticsCalculator StatisticsCalculator[T]
	ConfigValidator      ConfigValidator
	SplittingStrategy    SplittingStrategy[T]
}

// StoppingCriteria defines the interface for determining when to stop splitting
// This separates stopping logic from tree building logic
type StoppingCriteria[T comparable] interface {
	// ShouldCreateLeaf determines if a leaf node should be created
	ShouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool
}
```

---
### File: internals/training/builder/node_builder.go
```go
package builder

import (
	"fmt"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// DefaultNodeBuilder provides the default implementation of NodeBuilder interface
type DefaultNodeBuilder[T comparable] struct {
	config TreeConfiguration
}

// NewDefaultNodeBuilder creates a new default node builder
func NewDefaultNodeBuilder[T comparable](config TreeConfiguration) *DefaultNodeBuilder[T] {
	return &DefaultNodeBuilder[T]{
		config: config,
	}
}

// BuildLeafNode creates a leaf node from node statistics
func (nb *DefaultNodeBuilder[T]) BuildLeafNode(stats *NodeStatistics[T]) (*models.TreeNode, error) {
	if stats == nil {
		return nil, &BuilderError{
			Op:     "build_leaf_node",
			Field:  "statistics",
			Reason: "node statistics cannot be nil",
		}
	}

	// Convert class distribution using type-safe helper
	interfaceDistribution := nb.convertClassDistribution(stats.ClassDistribution)
	prediction := nb.convertPrediction(stats.MajorityClass)

	leaf, err := models.NewLeafNode(prediction, interfaceDistribution, stats.SampleCount)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_leaf_node",
			Reason: fmt.Sprintf("failed to create leaf node: %v", err),
			Err:    err,
		}
	}

	// Set additional statistics
	leaf.Confidence = stats.Confidence
	leaf.Impurity = stats.Impurity

	return leaf, nil
}

// BuildDecisionNode creates a decision node from split result and children
func (nb *DefaultNodeBuilder[T]) BuildDecisionNode(split *training.SplitResult[T], leftChild, rightChild *models.TreeNode) (*models.TreeNode, error) {
	if split == nil {
		return nil, &BuilderError{
			Op:     "build_decision_node",
			Field:  "split",
			Reason: "split result cannot be nil",
		}
	}

	if split.Feature == nil {
		return nil, &BuilderError{
			Op:     "build_decision_node",
			Field:  "feature",
			Reason: "split feature cannot be nil",
		}
	}

	var node *models.TreeNode
	var err error

	// Create appropriate decision node based on feature type
	switch split.Feature.Type {
	case models.NumericFeature, models.DateFeature:
		node, err = models.NewDecisionNode(split.Feature, split.Threshold)
		if err != nil {
			return nil, &BuilderError{
				Op:     "build_decision_node",
				Reason: fmt.Sprintf("failed to create numeric decision node: %v", err),
				Err:    err,
			}
		}

		// Set children for numeric split
		if leftChild != nil {
			if err := node.SetLeftChild(leftChild); err != nil {
				return nil, &BuilderError{
					Op:     "build_decision_node",
					Field:  "left_child",
					Reason: fmt.Sprintf("failed to set left child: %v", err),
					Err:    err,
				}
			}
		}

		if rightChild != nil {
			if err := node.SetRightChild(rightChild); err != nil {
				return nil, &BuilderError{
					Op:     "build_decision_node",
					Field:  "right_child",
					Reason: fmt.Sprintf("failed to set right child: %v", err),
					Err:    err,
				}
			}
		}

	case models.CategoricalFeature:
		node, err = models.NewCategoricalDecisionNode(split.Feature)
		if err != nil {
			return nil, &BuilderError{
				Op:     "build_decision_node",
				Reason: fmt.Sprintf("failed to create categorical decision node: %v", err),
				Err:    err,
			}
		}

		// For categorical splits, we would need to handle multiple children
		// This is a simplified implementation for binary splits
		if leftChild != nil {
			if err := node.SetChild("left", leftChild); err != nil {
				return nil, &BuilderError{
					Op:     "build_decision_node",
					Field:  "categorical_child",
					Reason: fmt.Sprintf("failed to set categorical child: %v", err),
					Err:    err,
				}
			}
		}

	default:
		return nil, &BuilderError{
			Op:     "build_decision_node",
			Field:  "feature_type",
			Value:  string(split.Feature.Type),
			Reason: "unsupported feature type for decision node",
		}
	}

	return node, nil
}

// convertClassDistribution safely converts generic class distribution to interface{} map
func (nb *DefaultNodeBuilder[T]) convertClassDistribution(distribution map[T]int) map[interface{}]int {
	if distribution == nil {
		return make(map[interface{}]int)
	}
	
	result := make(map[interface{}]int, len(distribution))
	for class, count := range distribution {
		result[interface{}(class)] = count
	}
	return result
}

// convertPrediction safely converts generic prediction to interface{}
func (nb *DefaultNodeBuilder[T]) convertPrediction(prediction T) interface{} {
	return interface{}(prediction)
}
```

---
### File: internals/training/builder/statistics_calculator.go
```go
package builder

import (
	"fmt"
	"math"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/models"
)

// DefaultStatisticsCalculator provides the default implementation of StatisticsCalculator interface
type DefaultStatisticsCalculator[T comparable] struct {
	config TreeConfiguration
}

// NewDefaultStatisticsCalculator creates a new default statistics calculator
func NewDefaultStatisticsCalculator[T comparable](config TreeConfiguration) *DefaultStatisticsCalculator[T] {
	return &DefaultStatisticsCalculator[T]{
		config: config,
	}
}

// CalculateNodeStatistics computes statistics for a dataset at a node
func (sc *DefaultStatisticsCalculator[T]) CalculateNodeStatistics(dataset training.Dataset[T]) (*NodeStatistics[T], error) {
	if dataset == nil {
		return nil, &BuilderError{
			Op:     "calculate_node_statistics",
			Field:  "dataset",
			Reason: "dataset cannot be nil",
		}
	}

	size := dataset.GetSize()
	if size == 0 {
		return nil, &BuilderError{
			Op:     "calculate_node_statistics",
			Field:  "dataset",
			Reason: "dataset cannot be empty",
		}
	}

	// Count class occurrences
	classDistribution := make(map[T]int)
	indices := dataset.GetIndices()

	for _, sampleIndex := range indices {
		target, err := dataset.GetTarget(sampleIndex)
		if err != nil {
			return nil, &BuilderError{
				Op:     "calculate_node_statistics",
				Field:  "target",
				Reason: fmt.Sprintf("failed to get target for sample %d: %v", sampleIndex, err),
				Err:    err,
			}
		}
		classDistribution[target]++
	}

	// Find majority class and calculate confidence
	var majorityClass T
	maxCount := 0
	for class, count := range classDistribution {
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}

	confidence := float64(maxCount) / float64(size)

	// Calculate impurity based on configured criterion
	impurity, err := sc.calculateImpurity(classDistribution, size)
	if err != nil {
		return nil, &BuilderError{
			Op:     "calculate_node_statistics",
			Field:  "impurity",
			Reason: fmt.Sprintf("failed to calculate impurity: %v", err),
			Err:    err,
		}
	}

	return &NodeStatistics[T]{
		SampleCount:       size,
		ClassDistribution: classDistribution,
		MajorityClass:     majorityClass,
		Confidence:        confidence,
		Impurity:          impurity,
	}, nil
}

// calculateImpurity calculates the impurity measure based on the configured criterion
func (sc *DefaultStatisticsCalculator[T]) calculateImpurity(classDistribution map[T]int, totalSamples int) (float64, error) {
	if totalSamples == 0 {
		return 0.0, nil
	}

	switch sc.config.GetCriterion() {
	case models.GiniCriterion:
		return sc.calculateGiniImpurity(classDistribution, totalSamples), nil
	case models.EntropyCriterion:
		return sc.calculateEntropyImpurity(classDistribution, totalSamples), nil
	default:
		return 0.0, &BuilderError{
			Op:     "calculate_impurity",
			Field:  "criterion",
			Value:  string(sc.config.GetCriterion()),
			Reason: "unsupported impurity criterion",
		}
	}
}

// calculateGiniImpurity calculates the Gini impurity
func (sc *DefaultStatisticsCalculator[T]) calculateGiniImpurity(classDistribution map[T]int, totalSamples int) float64 {
	if totalSamples == 0 {
		return 0.0
	}

	impurity := 1.0
	for _, count := range classDistribution {
		if count > 0 {
			prob := float64(count) / float64(totalSamples)
			impurity -= prob * prob
		}
	}

	return impurity
}

// calculateEntropyImpurity calculates the entropy impurity
func (sc *DefaultStatisticsCalculator[T]) calculateEntropyImpurity(classDistribution map[T]int, totalSamples int) float64 {
	if totalSamples == 0 {
		return 0.0
	}

	entropy := 0.0
	for _, count := range classDistribution {
		if count > 0 {
			prob := float64(count) / float64(totalSamples)
			entropy -= prob * math.Log2(prob)
		}
	}
	// Validate result for numerical stability
	if math.IsNaN(entropy) || math.IsInf(entropy, 0) {
		return 0.0 // Return safe default for invalid calculations
	}
	return entropy
}

// GetImpurityName returns the name of the impurity measure being used
func (sc *DefaultStatisticsCalculator[T]) GetImpurityName() string {
	return string(sc.config.GetCriterion())
}

// ValidateStatistics validates that the calculated statistics are reasonable
func (sc *DefaultStatisticsCalculator[T]) ValidateStatistics(stats *NodeStatistics[T]) error {
	if stats == nil {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "statistics",
			Reason: "statistics cannot be nil",
		}
	}

	if stats.SampleCount < 0 {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "sample_count",
			Value:  fmt.Sprintf("%d", stats.SampleCount),
			Reason: "sample count cannot be negative",
		}
	}

	if stats.Confidence < 0.0 || stats.Confidence > 1.0 {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "confidence",
			Value:  fmt.Sprintf("%.6f", stats.Confidence),
			Reason: "confidence must be between 0.0 and 1.0",
		}
	}

	if stats.Impurity < 0.0 {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "impurity",
			Value:  fmt.Sprintf("%.6f", stats.Impurity),
			Reason: "impurity cannot be negative",
		}
	}

	// Validate class distribution consistency
	totalCount := 0
	for _, count := range stats.ClassDistribution {
		if count < 0 {
			return &BuilderError{
				Op:     "validate_statistics",
				Field:  "class_distribution",
				Reason: "class counts cannot be negative",
			}
		}
		totalCount += count
	}

	if totalCount != stats.SampleCount {
		return &BuilderError{
			Op:     "validate_statistics",
			Field:  "class_distribution",
			Reason: fmt.Sprintf("distribution total (%d) does not match sample count (%d)", totalCount, stats.SampleCount),
		}
	}

	return nil
}
```

---
### File: internals/training/builder/stopping_criteria.go
```go
package builder

import (
	"fmt"

	"github.com/berrijam/mulberri/internals/training"
)

// DefaultStoppingCriteria provides the default implementation of StoppingCriteria interface
type DefaultStoppingCriteria[T comparable] struct {
	config TreeConfiguration
}

// NewDefaultStoppingCriteria creates a new default stopping criteria
func NewDefaultStoppingCriteria[T comparable](config TreeConfiguration) *DefaultStoppingCriteria[T] {
	return &DefaultStoppingCriteria[T]{
		config: config,
	}
}

// ShouldCreateLeaf determines if a leaf node should be created based on stopping criteria
func (sc *DefaultStoppingCriteria[T]) ShouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool {
	// Check maximum depth
	if depth >= sc.config.GetMaxDepth() {
		return true
	}

	// Check minimum samples for split
	if dataset.GetSize() < sc.config.GetMinSamplesSplit() {
		return true
	}

	// Check minimum samples for leaves (need at least 2 * minSamplesLeaf to split)
	if dataset.GetSize() < 2*sc.config.GetMinSamplesLeaf() {
		return true
	}

	// Check impurity threshold
	if impurity <= sc.config.GetMinImpurityDecrease() {
		return true
	}

	return false
}

// GetStoppingReason returns a human-readable reason why splitting should stop
func (sc *DefaultStoppingCriteria[T]) GetStoppingReason(dataset training.Dataset[T], depth int, impurity float64) string {
	if depth >= sc.config.GetMaxDepth() {
		return "maximum depth reached"
	}

	if dataset.GetSize() < sc.config.GetMinSamplesSplit() {
		return "insufficient samples for split"
	}

	if dataset.GetSize() < 2*sc.config.GetMinSamplesLeaf() {
		return "insufficient samples for leaves"
	}

	if impurity <= sc.config.GetMinImpurityDecrease() {
		return "impurity below threshold"
	}

	return "no stopping criteria met"
}

// ValidateStoppingCriteria validates that the stopping criteria configuration is reasonable
func (sc *DefaultStoppingCriteria[T]) ValidateStoppingCriteria() error {
	if sc.config.GetMaxDepth() <= 0 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", sc.config.GetMaxDepth()),
			Reason: "max depth must be positive",
		}
	}

	if sc.config.GetMinSamplesSplit() < 2 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_samples_split",
			Value:  fmt.Sprintf("%d", sc.config.GetMinSamplesSplit()), 
			Reason: "min samples split must be at least 2",
		}
	}

	if sc.config.GetMinSamplesLeaf() < 1 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_samples_leaf",
			Value:  fmt.Sprintf("%d", sc.config.GetMinSamplesLeaf()),
			Reason: "min samples leaf must be at least 1",
		}
	}

	if sc.config.GetMinSamplesLeaf() > sc.config.GetMinSamplesSplit() {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_samples_leaf",
			Reason: "min samples leaf cannot exceed min samples split",
		}
	}

	if sc.config.GetMinImpurityDecrease() < 0.0 {
		return &BuilderError{
			Op:     "validate_stopping_criteria",
			Field:  "min_impurity_decrease",
			Reason: "min impurity decrease cannot be negative",
		}
	}

	return nil
}

// EstimateLeafCount estimates the number of leaf nodes that will be created
func (sc *DefaultStoppingCriteria[T]) EstimateLeafCount(totalSamples int) int {
	if totalSamples <= sc.config.GetMinSamplesLeaf() {
		return 1
	}

	// Rough estimation based on binary splits
	maxPossibleLeaves := totalSamples / sc.config.GetMinSamplesLeaf()

	// Limit by maximum depth (2^depth leaves at most)
	maxDepthLeaves := 1 << sc.config.GetMaxDepth()

	if maxPossibleLeaves < maxDepthLeaves {
		return maxPossibleLeaves
	}
	return maxDepthLeaves
}

// ShouldPrune determines if a subtree should be pruned (for future pruning support)
func (sc *DefaultStoppingCriteria[T]) ShouldPrune(nodeImpurity, leftImpurity, rightImpurity float64, leftSamples, rightSamples int) bool {
	// Simple pruning criterion: if the weighted impurity doesn't improve significantly
	totalSamples := leftSamples + rightSamples
	if totalSamples == 0 {
		return true
	}

	weightedImpurity := (float64(leftSamples)*leftImpurity + float64(rightSamples)*rightImpurity) / float64(totalSamples)
	impurityGain := nodeImpurity - weightedImpurity

	return impurityGain < sc.config.GetMinImpurityDecrease()
}
```

---
### File: internals/training/builder/tree_builder.go
```go
// Package builder provides tree construction functionality using the C4.5 splitting algorithm.
// It integrates the training package's C45Splitter with the models package's tree structures.
package builder

import (
	"context"
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// TreeBuilder constructs decision trees using the C4.5 algorithm
type TreeBuilder[T comparable] struct {
	// Core components
	nodeBuilder          NodeBuilder[T]
	statisticsCalculator StatisticsCalculator[T]
	stoppingCriteria     StoppingCriteria[T]
	configValidator      ConfigValidator
	splittingStrategy    SplittingStrategy[T]
	splitter             *training.C45Splitter[T]
	config               BuilderConfig
}

// C45SplittingStrategy wraps the C45Splitter to implement SplittingStrategy interface
type C45SplittingStrategy[T comparable] struct {
	splitter *training.C45Splitter[T]
}

// NewC45SplittingStrategy creates a C45 splitting strategy
func NewC45SplittingStrategy[T comparable](splitter *training.C45Splitter[T]) *C45SplittingStrategy[T] {
	return &C45SplittingStrategy[T]{
		splitter: splitter,
	}
}

// FindBestSplit finds the best split for the given dataset and features
func (css *C45SplittingStrategy[T]) FindBestSplit(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*training.SplitResult[T], error) {
	return css.splitter.FindBestSplit(ctx, dataset, features)
}

// TreeConfiguration defines the interface for tree configuration
type TreeConfiguration interface {
	GetMaxDepth() int
	GetMinSamplesSplit() int
	GetMinSamplesLeaf() int
	GetMinImpurityDecrease() float64
	GetCriterion() models.SplitCriterion
	GetTargetType() models.FeatureType
	GetEnableLogging() bool
	Validate() error
	ToModelOptions() []models.TreeOption
}

// UnifiedTreeConfig provides a single source of truth for tree configuration
type UnifiedTreeConfig struct {
	MaxDepth            int                   `json:"max_depth"`
	MinSamplesSplit     int                   `json:"min_samples_split"`
	MinSamplesLeaf      int                   `json:"min_samples_leaf"`
	MinImpurityDecrease float64               `json:"min_impurity_decrease"`
	Criterion           models.SplitCriterion `json:"criterion"`
	TargetType          models.FeatureType    `json:"target_type"`
	EnableLogging       bool                  `json:"enable_logging"`
}

// BuilderConfig is kept for backward compatibility but now wraps UnifiedTreeConfig
type BuilderConfig struct {
	*UnifiedTreeConfig
}

// inferTargetType automatically infers the target type from the generic type parameter
func inferTargetType[T comparable]() models.FeatureType {
	var zero T
	switch any(zero).(type) {
	case string:
		return models.CategoricalFeature
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return models.NumericFeature
	default:
		return models.CategoricalFeature // Safe default for other comparable types
	}
}

// DefaultUnifiedTreeConfig returns sensible default configuration
func DefaultUnifiedTreeConfig() *UnifiedTreeConfig {
	return &UnifiedTreeConfig{
		MaxDepth:            10,
		MinSamplesSplit:     2,
		MinSamplesLeaf:      1,
		MinImpurityDecrease: 0.0,
		Criterion:           models.EntropyCriterion, // Match training package default
		TargetType:          models.CategoricalFeature,
		EnableLogging:       false,
	}
}

// DefaultUnifiedTreeConfigForType returns default configuration with inferred target type
func DefaultUnifiedTreeConfigForType[T comparable]() *UnifiedTreeConfig {
	config := DefaultUnifiedTreeConfig()
	config.TargetType = inferTargetType[T]()
	return config
}

// DefaultBuilderConfig returns sensible default configuration (backward compatibility)
func DefaultBuilderConfig() BuilderConfig {
	return BuilderConfig{
		UnifiedTreeConfig: DefaultUnifiedTreeConfig(),
	}
}

// Interface implementation for UnifiedTreeConfig
func (c *UnifiedTreeConfig) GetMaxDepth() int {
	return c.MaxDepth
}

func (c *UnifiedTreeConfig) GetMinSamplesSplit() int {
	return c.MinSamplesSplit
}

func (c *UnifiedTreeConfig) GetMinSamplesLeaf() int {
	return c.MinSamplesLeaf
}

func (c *UnifiedTreeConfig) GetMinImpurityDecrease() float64 {
	return c.MinImpurityDecrease
}

func (c *UnifiedTreeConfig) GetCriterion() models.SplitCriterion {
	return c.Criterion
}

func (c *UnifiedTreeConfig) GetTargetType() models.FeatureType {
	return c.TargetType
}

func (c *UnifiedTreeConfig) GetEnableLogging() bool {
	return c.EnableLogging
}

func (c *UnifiedTreeConfig) Validate() error {

	if c.MinSamplesLeaf > c.MinSamplesSplit {
		return &BuilderError{
			Op: "validate_config",
			Reason: fmt.Sprintf("min_samples_leaf (%d) cannot be greater than min_samples_split (%d)",
				c.MinSamplesLeaf, c.MinSamplesSplit),
		}
	}

	if c.MinImpurityDecrease < 0.0 {
		return &BuilderError{
			Op:     "validate_config",
			Field:  "min_impurity_decrease",
			Value:  fmt.Sprintf("%.6f", c.MinImpurityDecrease),
			Reason: "minimum impurity decrease cannot be negative",
		}
	}

	return nil
}

func (c *UnifiedTreeConfig) ToModelOptions() []models.TreeOption {
	return []models.TreeOption{
		models.WithMaxDepth(c.MaxDepth),
		models.WithMinSamples(c.MinSamplesSplit),
		models.WithTargetType(c.TargetType),
		models.WithCriterion(c.Criterion),
	}
}

// BuilderOption defines functional options for builder configuration
type BuilderOption func(*BuilderConfig) error

// WithMaxDepth sets the maximum tree depth
func WithMaxDepth(depth int) BuilderOption {
	return func(config *BuilderConfig) error {
		if depth <= 0 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "max_depth",
				Value:  fmt.Sprintf("%d", depth),
				Reason: "max depth must be positive",
			}
		}
		config.MaxDepth = depth
		return nil
	}
}

// WithMinSamplesSplit sets minimum samples required to split a node
// Includes cross-parameter validation with MinSamplesLeaf
func WithMinSamplesSplit(samples int) BuilderOption {
	return func(config *BuilderConfig) error {
		if samples < 2 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "min_samples_split",
				Value:  fmt.Sprintf("%d", samples),
				Reason: "minimum samples split must be >= 2",
			}
		}

		// Cross-parameter validation: MinSamplesSplit cannot be less than MinSamplesLeaf
		if config.MinSamplesLeaf > 0 && samples < config.MinSamplesLeaf {
			return &BuilderError{
				Op:    "configure_builder",
				Field: "min_samples_split",
				Value: fmt.Sprintf("%d", samples),
				Reason: fmt.Sprintf("min_samples_split (%d) cannot be less than min_samples_leaf (%d)",
					samples, config.MinSamplesLeaf),
			}
		}

		config.MinSamplesSplit = samples
		return nil
	}
}

// WithMinSamplesLeaf sets minimum samples required in a leaf node
// Includes cross-parameter validation with MinSamplesSplit
func WithMinSamplesLeaf(samples int) BuilderOption {
	return func(config *BuilderConfig) error {
		if samples < 1 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "min_samples_leaf",
				Value:  fmt.Sprintf("%d", samples),
				Reason: "minimum samples leaf must be >= 1",
			}
		}

		// Cross-parameter validation: MinSamplesLeaf cannot exceed MinSamplesSplit
		if config.MinSamplesSplit > 0 && samples > config.MinSamplesSplit {
			return &BuilderError{
				Op:    "configure_builder",
				Field: "min_samples_leaf",
				Value: fmt.Sprintf("%d", samples),
				Reason: fmt.Sprintf("min_samples_leaf (%d) cannot exceed min_samples_split (%d)",
					samples, config.MinSamplesSplit),
			}
		}

		config.MinSamplesLeaf = samples
		return nil
	}
}

// WithMinImpurityDecrease sets minimum impurity decrease required for split
func WithMinImpurityDecrease(decrease float64) BuilderOption {
	return func(config *BuilderConfig) error {
		if decrease < 0.0 {
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "min_impurity_decrease",
				Value:  fmt.Sprintf("%.6f", decrease),
				Reason: "minimum impurity decrease cannot be negative",
			}
		}
		config.MinImpurityDecrease = decrease
		return nil
	}
}

// WithCriterion sets the split criterion
func WithCriterion(criterion models.SplitCriterion) BuilderOption {
	return func(config *BuilderConfig) error {
		switch criterion {
		case models.GiniCriterion, models.EntropyCriterion, models.MSECriterion:
			config.Criterion = criterion
			return nil
		default:
			return &BuilderError{
				Op:     "configure_builder",
				Field:  "criterion",
				Value:  string(criterion),
				Reason: "invalid split criterion, must be 'gini', 'entropy', or 'mse'",
			}
		}
	}
}

// WithTargetType sets the target variable type
func WithTargetType(targetType models.FeatureType) BuilderOption {
	return func(config *BuilderConfig) error {
		config.TargetType = targetType
		return nil
	}
}

// WithLogging enables or disables logging
func WithLogging(enabled bool) BuilderOption {
	return func(config *BuilderConfig) error {
		config.EnableLogging = enabled
		return nil
	}
}

// NewTreeBuilder creates a new tree builder with the given splitter and options
func NewTreeBuilder[T comparable](splitter *training.C45Splitter[T], options ...BuilderOption) (*TreeBuilder[T], error) {
	if splitter == nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Field:  "splitter",
			Reason: "splitter cannot be nil",
		}
	}

	config := DefaultBuilderConfig()

	// Apply functional options
	for _, option := range options {
		if err := option(&config); err != nil {
			return nil, &BuilderError{
				Op:     "create_builder",
				Reason: fmt.Sprintf("invalid option: %v", err),
				Err:    err,
			}
		}
	}

	// Validate unified configuration
	if err := config.UnifiedTreeConfig.Validate(); err != nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Reason: fmt.Sprintf("configuration validation failed: %v", err),
			Err:    err,
		}
	}

	// Create components
	nodeBuilder := NewDefaultNodeBuilder[T](config.UnifiedTreeConfig)
	statisticsCalculator := NewDefaultStatisticsCalculator[T](config.UnifiedTreeConfig)
	stoppingCriteria := NewDefaultStoppingCriteria[T](config.UnifiedTreeConfig)
	configValidator := NewDefaultConfigValidator(config.UnifiedTreeConfig)
	splittingStrategy := NewC45SplittingStrategy[T](splitter)

	// Create builder instance
	builder := &TreeBuilder[T]{
		nodeBuilder:          nodeBuilder,
		statisticsCalculator: statisticsCalculator,
		stoppingCriteria:     stoppingCriteria,
		configValidator:      configValidator,
		splittingStrategy:    splittingStrategy,
		splitter:             splitter,
		config:               config,
	}

	// Validate configuration
	if err := builder.configValidator.ValidateConfiguration(); err != nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Reason: fmt.Sprintf("configuration validation failed: %v", err),
			Err:    err,
		}
	}

	// Validate type compatibility
	if err := builder.validateTypeCompatibility(); err != nil {
		return nil, &BuilderError{
			Op:     "create_builder",
			Reason: fmt.Sprintf("type compatibility validation failed: %v", err),
			Err:    err,
		}
	}

	return builder, nil
}

// NewTreeBuilderWithInference creates a tree builder with automatic target type inference
func NewTreeBuilderWithInference[T comparable](splitter *training.C45Splitter[T], options ...BuilderOption) (*TreeBuilder[T], error) {
	// Start with type-aware default configuration
	config := BuilderConfig{
		UnifiedTreeConfig: DefaultUnifiedTreeConfigForType[T](),
	}

	// Apply functional options
	for _, option := range options {
		if err := option(&config); err != nil {
			return nil, &BuilderError{
				Op:     "create_builder_with_inference",
				Reason: fmt.Sprintf("invalid option: %v", err),
				Err:    err,
			}
		}
	}

	// Use the standard constructor with the configured options
	return NewTreeBuilder(splitter, func(c *BuilderConfig) error {
		*c = config
		return nil
	})
}

// BuildTree constructs a complete decision tree from the dataset
func (b *TreeBuilder[T]) BuildTree(ctx context.Context, dataset training.Dataset[T], features []*models.Feature) (*models.DecisionTree, error) {
	if err := b.validateBuildInputs(dataset, features); err != nil {
		return nil, err
	}

	if b.config.GetEnableLogging() {
		logger.Info(fmt.Sprintf("Starting tree construction with %d samples and %d features", dataset.GetSize(), len(features)))
	}

	// Create the decision tree container
	tree, err := b.createDecisionTreeContainer(features)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_tree",
			Reason: fmt.Sprintf("failed to create tree container: %v", err),
			Err:    err,
		}
	}

	// Build the root node
	root, err := b.buildNode(ctx, dataset, features, 0, "root")
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_tree",
			Reason: fmt.Sprintf("failed to build root node: %v", err),
			Err:    err,
		}
	}

	tree.Root = root
	tree.UpdateStatistics()

	if b.config.GetEnableLogging() {
		logger.Info(fmt.Sprintf("Tree construction completed: %d nodes, %d leaves, depth %d", tree.NodeCount, tree.LeafCount, tree.Depth))
	}

	return tree, nil
}

// validateBuildInputs performs input validation
func (b *TreeBuilder[T]) validateBuildInputs(dataset training.Dataset[T], features []*models.Feature) error {
	var errors training.MultiError

	// Dataset validation
	if dataset == nil {
		errors.Add(&BuilderError{
			Op:     "validate_build_inputs",
			Field:  "dataset",
			Reason: "dataset cannot be nil",
		})
	} else {
		if dataset.GetSize() == 0 {
			errors.Add(&BuilderError{
				Op:     "validate_build_inputs",
				Field:  "dataset",
				Reason: "dataset cannot be empty",
			})
		} else {
			// Check dataset size against configuration
			datasetSize := dataset.GetSize()
			if datasetSize < b.config.GetMinSamplesSplit() {
				errors.Add(&BuilderError{
					Op:    "validate_build_inputs",
					Field: "dataset_size",
					Value: fmt.Sprintf("%d", datasetSize),
					Reason: fmt.Sprintf("dataset size (%d) is smaller than min_samples_split (%d)",
						datasetSize, b.config.GetMinSamplesSplit()),
				})
			}

			// Validate that dataset has sufficient samples for meaningful tree
			if datasetSize < 2*b.config.GetMinSamplesLeaf() {
				errors.Add(&BuilderError{
					Op:    "validate_build_inputs",
					Field: "dataset_size",
					Value: fmt.Sprintf("%d", datasetSize),
					Reason: fmt.Sprintf("dataset size (%d) is too small for meaningful tree construction (need at least %d samples)",
						datasetSize, 2*b.config.GetMinSamplesLeaf()),
				})
			}
		}
	}

	// Features validation
	if len(features) == 0 {
		errors.Add(&BuilderError{
			Op:     "validate_build_inputs",
			Field:  "features",
			Reason: "features cannot be empty",
		})
	} else {
		// Check for feature name uniqueness
		featureNames := make(map[string]int)

		// Validate each feature
		for i, feature := range features {
			if feature == nil {
				errors.Add(&BuilderError{
					Op:     "validate_build_inputs",
					Field:  "features",
					Value:  fmt.Sprintf("%d", i),
					Reason: fmt.Sprintf("feature at index %d is nil", i),
				})
				continue
			}

			// Check for duplicate feature names
			if prevIndex, exists := featureNames[feature.Name]; exists {
				errors.Add(&BuilderError{
					Op:    "validate_build_inputs",
					Field: "feature_names",
					Value: feature.Name,
					Reason: fmt.Sprintf("duplicate feature name '%s' found at indices %d and %d",
						feature.Name, prevIndex, i),
				})
			} else {
				featureNames[feature.Name] = i
			}

			// Validate individual feature
			if err := feature.Validate(); err != nil {
				errors.Add(&BuilderError{
					Op:     "validate_build_inputs",
					Field:  "feature_validation",
					Value:  feature.Name,
					Reason: fmt.Sprintf("feature '%s' validation failed: %v", feature.Name, err),
					Err:    err,
				})
			}

			// Check feature column bounds
			if feature.ColumnNumber < 0 {
				errors.Add(&BuilderError{
					Op:     "validate_build_inputs",
					Field:  "feature_column",
					Value:  fmt.Sprintf("%d", feature.ColumnNumber),
					Reason: fmt.Sprintf("feature '%s' has invalid column number %d", feature.Name, feature.ColumnNumber),
				})
			}
		}
	}

	// Cross-validation between dataset and configuration
	if dataset != nil && len(features) > 0 {
		// Validate that the configuration is reasonable for the dataset size
		if err := b.configValidator.ValidateForDataset(dataset.GetSize(), len(features)); err != nil {
			errors.Add(&BuilderError{
				Op:     "validate_build_inputs",
				Field:  "config_dataset_compatibility",
				Reason: fmt.Sprintf("configuration incompatible with dataset: %v", err),
				Err:    err,
			})
		}
	}

	if errors.HasErrors() {
		return &BuilderError{
			Op:     "validate_build_inputs",
			Reason: "input validation failed",
			Err:    &errors,
		}
	}

	return nil
}

// createDecisionTreeContainer creates the tree container with proper feature setup
func (b *TreeBuilder[T]) createDecisionTreeContainer(features []*models.Feature) (*models.DecisionTree, error) {
	// Use unified configuration to create tree with consistent options
	tree, err := models.NewDecisionTreeWithOptions(b.config.UnifiedTreeConfig.ToModelOptions()...)
	if err != nil {
		return nil, &BuilderError{
			Op:     "create_tree_container",
			Field:  "tree_creation",
			Reason: fmt.Sprintf("failed to create tree with unified config: %v", err),
			Err:    err,
		}
	}

	// Add features to the tree
	for i, feature := range features {
		logger.Debug(feature.Name + string(feature.Type) + strconv.Itoa(i))
		_, err := tree.AddFeature(feature.Name, feature.Type)
		if err != nil {
			return nil, &BuilderError{
				Op:     "create_tree_container",
				Field:  "add_feature",
				Value:  feature.Name,
				Reason: fmt.Sprintf("failed to add feature: %v", err),
				Err:    err,
			}
		}
	}

	return tree, nil
}

// buildNode recursively constructs tree nodes
func (b *TreeBuilder[T]) buildNode(ctx context.Context, dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string) (*models.TreeNode, error) {
	// Check context cancellation
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	if b.config.GetEnableLogging() && depth <= 2 {
		logger.Debug(fmt.Sprintf("Building node %s at depth %d with %d samples", nodeID, depth, dataset.GetSize()))
	}

	// Calculate node statistics
	nodeStats, err := b.calculateNodeStatistics(dataset)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_node",
			Field:  "node_statistics",
			Reason: fmt.Sprintf("failed to calculate node statistics: %v", err),
			Err:    err,
		}
	}

	// Check stopping criteria
	if b.shouldCreateLeaf(dataset, depth, nodeStats.impurity) {
		return b.createLeafNode(nodeStats)
	}

	// Find best split using the C4.5 splitter
	split, err := b.splitter.FindBestSplit(ctx, dataset, features)
	if err != nil {
		return nil, &BuilderError{
			Op:     "build_node",
			Field:  "find_split",
			Reason: fmt.Sprintf("failed to find split: %v", err),
			Err:    err,
		}
	}

	// If no beneficial split found, create leaf
	if split == nil || split.GainRatio <= b.config.GetMinImpurityDecrease() {
		if b.config.GetEnableLogging() && depth <= 2 {
			logger.Debug(fmt.Sprintf("No beneficial split found for node %s, creating leaf", nodeID))
		}
		return b.createLeafNode(nodeStats)
	}

	// Create decision node from split
	return b.createDecisionNodeFromSplit(ctx, split, dataset, features, depth, nodeID, nodeStats)
}

// shouldCreateLeaf determines if a leaf node should be created
func (b *TreeBuilder[T]) shouldCreateLeaf(dataset training.Dataset[T], depth int, impurity float64) bool {
	return b.stoppingCriteria.ShouldCreateLeaf(dataset, depth, impurity)
}

// nodeStatistics holds calculated statistics for a node
type nodeStatistics[T comparable] struct {
	sampleCount       int
	classDistribution map[T]int
	majorityClass     T
	confidence        float64
	impurity          float64
}

// calculateNodeStatistics computes statistics for the current dataset
func (b *TreeBuilder[T]) calculateNodeStatistics(dataset training.Dataset[T]) (*nodeStatistics[T], error) {
	// Use the statistics calculator
	stats, err := b.statisticsCalculator.CalculateNodeStatistics(dataset)
	if err != nil {
		return nil, err
	}

	return &nodeStatistics[T]{
		sampleCount:       stats.SampleCount,
		classDistribution: stats.ClassDistribution,
		majorityClass:     stats.MajorityClass,
		confidence:        stats.Confidence,
		impurity:          stats.Impurity,
	}, nil
}

// createLeafNode creates a leaf node with prediction
func (b *TreeBuilder[T]) createLeafNode(stats *nodeStatistics[T]) (*models.TreeNode, error) {
	newStats := &NodeStatistics[T]{
		SampleCount:       stats.sampleCount,
		ClassDistribution: stats.classDistribution,
		MajorityClass:     stats.majorityClass,
		Confidence:        stats.confidence,
		Impurity:          stats.impurity,
	}

	// Use the node builder
	return b.nodeBuilder.BuildLeafNode(newStats)
}

// convertClassDistribution safely converts generic class distribution to interface{} map
// This provides a single point of type conversion with clear semantics and maintains
// type safety by centralizing the conversion logic. This addresses the type safety
// issue identified in the code review by eliminating scattered interface{} conversions.
func (b *TreeBuilder[T]) convertClassDistribution(distribution map[T]int) map[interface{}]int {
	if distribution == nil {
		return make(map[interface{}]int)
	}

	result := make(map[interface{}]int, len(distribution))
	for class, count := range distribution {
		result[interface{}(class)] = count
	}
	return result
}

// convertPrediction safely converts generic prediction to interface{}
// This provides a single point of type conversion with clear semantics and maintains
// compile-time type safety within the builder while providing compatibility with
// the existing models package interface.
func (b *TreeBuilder[T]) convertPrediction(prediction T) interface{} {
	return interface{}(prediction)
}

// validateTypeCompatibility ensures the generic type T is compatible with the target type
func (b *TreeBuilder[T]) validateTypeCompatibility() error {
	var zero T
	targetType := b.config.GetTargetType()

	switch targetType {
	case models.CategoricalFeature:
		// Categorical features can accept any comparable type
		return nil
	case models.NumericFeature:
		// Numeric features should only accept numeric types
		switch any(zero).(type) {
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
			return nil
		default:
			return &BuilderError{
				Op:     "validate_type_compatibility",
				Field:  "target_type",
				Value:  string(targetType),
				Reason: fmt.Sprintf("generic type %T is not compatible with numeric target type", zero),
			}
		}
	default:
		return &BuilderError{
			Op:     "validate_type_compatibility",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "unsupported target type",
		}
	}
}

// createDecisionNodeFromSplit creates a decision node and its children from a split result
func (b *TreeBuilder[T]) createDecisionNodeFromSplit(ctx context.Context, split *training.SplitResult[T], dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string, stats *nodeStatistics[T]) (*models.TreeNode, error) {
	var node *models.TreeNode
	var err error

	// Create appropriate node type based on feature type
	switch split.Feature.Type {
	case models.NumericFeature, models.DateFeature:
		node, err = models.NewDecisionNode(split.Feature, split.Threshold)
		if err != nil {
			return nil, &BuilderError{
				Op:     "create_decision_node",
				Field:  "numeric_node",
				Reason: fmt.Sprintf("failed to create numeric decision node: %v", err),
				Err:    err,
			}
		}

		// Build children for binary split
		if err := b.buildBinaryChildren(ctx, node, split, dataset, features, depth, nodeID); err != nil {
			return nil, err
		}

	case models.CategoricalFeature:
		node, err = models.NewCategoricalDecisionNode(split.Feature)
		if err != nil {
			return nil, &BuilderError{
				Op:     "create_decision_node",
				Field:  "categorical_node",
				Reason: fmt.Sprintf("failed to create categorical decision node: %v", err),
				Err:    err,
			}
		}

		// Build children for categorical split
		if err := b.buildCategoricalChildren(ctx, node, split, dataset, features, depth, nodeID); err != nil {
			return nil, err
		}

	default:
		return nil, &BuilderError{
			Op:     "create_decision_node",
			Field:  "feature_type",
			Value:  string(split.Feature.Type),
			Reason: "unsupported feature type for decision node",
		}
	}

	// Set node statistics
	node.Samples = stats.sampleCount
	node.Confidence = stats.confidence
	node.Impurity = stats.impurity

	// Convert and set class distribution using type-safe helper
	node.ClassDistribution = b.convertClassDistribution(stats.classDistribution)

	return node, nil
}

// buildBinaryChildren builds left and right children for numeric splits
func (b *TreeBuilder[T]) buildBinaryChildren(ctx context.Context, node *models.TreeNode, split *training.SplitResult[T], dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string) error {
	// Build left child (values <= threshold)
	if len(split.LeftIndices) >= b.config.GetMinSamplesLeaf() {
		leftDataset := dataset.Subset(split.LeftIndices)
		leftChild, err := b.buildNode(ctx, leftDataset, features, depth+1, nodeID+"_L")
		if err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "left_child",
				Reason: fmt.Sprintf("failed to build left child: %v", err),
				Err:    err,
			}
		}

		if err := node.SetLeftChild(leftChild); err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "set_left_child",
				Reason: fmt.Sprintf("failed to set left child: %v", err),
				Err:    err,
			}
		}
	}

	// Build right child (values > threshold)
	if len(split.RightIndices) >= b.config.GetMinSamplesLeaf() {
		rightDataset := dataset.Subset(split.RightIndices)
		rightChild, err := b.buildNode(ctx, rightDataset, features, depth+1, nodeID+"_R")
		if err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "right_child",
				Reason: fmt.Sprintf("failed to build right child: %v", err),
				Err:    err,
			}
		}

		if err := node.SetRightChild(rightChild); err != nil {
			return &BuilderError{
				Op:     "build_binary_children",
				Field:  "set_right_child",
				Reason: fmt.Sprintf("failed to set right child: %v", err),
				Err:    err,
			}
		}
	}

	return nil
}

// buildCategoricalChildren builds children for categorical splits
func (b *TreeBuilder[T]) buildCategoricalChildren(ctx context.Context, node *models.TreeNode, split *training.SplitResult[T], dataset training.Dataset[T], features []*models.Feature, depth int, nodeID string) error {
	childIndex := 0
	var errors training.MultiError

	for category, indices := range split.Partitions {
		if len(indices) >= b.config.GetMinSamplesLeaf() {
			childDataset := dataset.Subset(indices)
			childNodeID := fmt.Sprintf("%s_C%d", nodeID, childIndex)

			child, err := b.buildNode(ctx, childDataset, features, depth+1, childNodeID)
			if err != nil {
				errors.Add(&BuilderError{
					Op:     "build_categorical_children",
					Field:  "child_node",
					Value:  fmt.Sprintf("%v", category),
					Reason: fmt.Sprintf("failed to build child for category: %v", err),
					Err:    err,
				})
				continue
			}

			if err := node.SetChild(category, child); err != nil {
				errors.Add(&BuilderError{
					Op:     "build_categorical_children",
					Field:  "set_child",
					Value:  fmt.Sprintf("%v", category),
					Reason: fmt.Sprintf("failed to set child: %v", err),
					Err:    err,
				})
				continue
			}

			childIndex++
		}
	}

	if errors.HasErrors() {
		return &BuilderError{
			Op:     "build_categorical_children",
			Reason: "failed to build categorical children",
			Err:    &errors,
		}
	}

	return nil
}

// GetConfig returns the current builder configuration
func (b *TreeBuilder[T]) GetConfig() BuilderConfig {
	return b.config
}

// GetSplitter returns the underlying splitter (for debugging/testing)
func (b *TreeBuilder[T]) GetSplitter() *training.C45Splitter[T] {
	return b.splitter
}

// GetNodeBuilder returns the node builder component
func (b *TreeBuilder[T]) GetNodeBuilder() NodeBuilder[T] {
	return b.nodeBuilder
}

// GetStatisticsCalculator returns the statistics calculator component
func (b *TreeBuilder[T]) GetStatisticsCalculator() StatisticsCalculator[T] {
	return b.statisticsCalculator
}

// GetStoppingCriteria returns the stopping criteria component
func (b *TreeBuilder[T]) GetStoppingCriteria() StoppingCriteria[T] {
	return b.stoppingCriteria
}

// GetConfigValidator returns the config validator component
func (b *TreeBuilder[T]) GetConfigValidator() ConfigValidator {
	return b.configValidator
}

// GetSplittingStrategy returns the splitting strategy component
func (b *TreeBuilder[T]) GetSplittingStrategy() SplittingStrategy[T] {
	return b.splittingStrategy
}
```

---
### File: internals/training/cache.go
```go
package training

import (
	"fmt"
	"sync"
	"time"

	"github.com/berrijam/mulberri/pkg/models"
)

// FeatureValueCache stores cached feature values to avoid repeated extraction
// with proper memory management and bounds checking
type FeatureValueCache[T comparable] struct {
	values     map[string]map[int]interface{} // featureID -> sampleIndex -> value
	timestamps map[string]time.Time           // featureID -> last access time
	mu         sync.RWMutex                   // Protects concurrent access
	limits     ResourceLimits                 // Resource limits for cache management
}

// NewFeatureValueCache creates a new feature value cache with resource limits
func NewFeatureValueCache[T comparable](limits ResourceLimits) *FeatureValueCache[T] {
	return &FeatureValueCache[T]{
		values:     make(map[string]map[int]interface{}),
		timestamps: make(map[string]time.Time),
		limits:     limits,
	}
}

// Get retrieves a cached feature value with bounds checking
func (c *FeatureValueCache[T]) Get(featureID string, sampleIdx int) (interface{}, bool) {
		c.mu.RLock()
	defer c.mu.RUnlock()
	
	if featureMap, exists := c.values[featureID]; exists {
		if value, exists := featureMap[sampleIdx]; exists {
			return value, true
		}
	}
	return nil, false
}

// Set stores a feature value in cache with size limits
func (c *FeatureValueCache[T]) Set(featureID string, sampleIdx int, value interface{}) {
	if featureID == "" || sampleIdx < 0 {
		return
	}
	
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Check cache size limits
	totalSize := 0
	for _, featureMap := range c.values {
		totalSize += len(featureMap)
	}
	
	if totalSize >= c.limits.MaxCacheSize {
		c.evictOldEntries()
	}
	
	if c.values[featureID] == nil {
		c.values[featureID] = make(map[int]interface{})
	}
	c.values[featureID][sampleIdx] = value
	c.timestamps[featureID] = time.Now()
}

// evictOldEntries removes old cache entries (must be called with write lock held)
func (c *FeatureValueCache[T]) evictOldEntries() {
	cutoff := time.Now().Add(-c.limits.MaxCacheAge)
	for featureID, timestamp := range c.timestamps {
		if timestamp.Before(cutoff) {
			delete(c.values, featureID)
			delete(c.timestamps, featureID)
		}
	}
}

// PreloadFeature loads all feature values for a dataset into cache
func (c *FeatureValueCache[T]) precacheFeature(dataset Dataset[T], feature *models.Feature) error {
	if dataset == nil || feature == nil {
		return &ValidationError{
			Field:  "preload_params",
			Reason: "dataset and feature cannot be nil",
		}
	}
	
	c.mu.Lock()
	defer c.mu.Unlock()
	
	featureID := feature.Name
	if c.values[featureID] == nil {
		c.values[featureID] = make(map[int]interface{})
	}
	
	var errors MultiError
	for _, idx := range dataset.GetIndices() {
		if _, exists := c.values[featureID][idx]; !exists {
			value, err := dataset.GetFeatureValue(idx, feature)
			if err != nil {
				errors.Add(fmt.Errorf("failed to get value for sample %d: %w", idx, err))
				continue
			}
			c.values[featureID][idx] = value
		}
	}
	
	c.timestamps[featureID] = time.Now()
	
	if errors.HasErrors() {
		return &errors
	}
	return nil
}

// Clear removes all cached values (useful for testing and memory management)
func (c *FeatureValueCache[T]) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.values = make(map[string]map[int]interface{})
	c.timestamps = make(map[string]time.Time)
}

// Size returns the total number of cached values
func (c *FeatureValueCache[T]) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	totalSize := 0
	for _, featureMap := range c.values {
		totalSize += len(featureMap)
	}
	return totalSize
}

// PreSortedFeatureData stores pre-sorted numeric feature data
type PreSortedFeatureData[T comparable] struct {
	Feature   *models.Feature    // The feature this data belongs to
	Values    []numericValue[T]  // Pre-sorted numeric values
	Sorted    bool               // Whether the values are sorted
	Timestamp time.Time          // When this data was created/last accessed
}

// NumericFeatureCache stores pre-sorted numeric features with proper cleanup
type NumericFeatureCache[T comparable] struct {
	cache  map[string]*PreSortedFeatureData[T] // featureID -> pre-sorted data
	mu     sync.RWMutex                        // Protects concurrent access
	limits ResourceLimits                      // Resource limits for cache management
}

// NewNumericFeatureCache creates a new numeric feature cache
func NewNumericFeatureCache[T comparable](limits ResourceLimits) *NumericFeatureCache[T] {
	return &NumericFeatureCache[T]{
		cache:  make(map[string]*PreSortedFeatureData[T]),
		limits: limits,
	}
}

// Get retrieves cached pre-sorted data
func (c *NumericFeatureCache[T]) Get(featureID string) (*PreSortedFeatureData[T], bool) {
	if featureID == "" {
		return nil, false
	}
	
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	data, exists := c.cache[featureID]
	if exists && time.Since(data.Timestamp) > c.limits.MaxCacheAge {
		return nil, false // Expired
	}
	return data, exists
}

// Set stores pre-sorted data with size limits
func (c *NumericFeatureCache[T]) Set(featureID string, data *PreSortedFeatureData[T]) {
	if featureID == "" || data == nil {
		return
	}
	
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Check cache size and evict if necessary
	if len(c.cache) >= c.limits.MaxCacheSize/10 { // Reasonable limit for expensive pre-sorted data
		c.evictOldEntries()
	}
	
	data.Timestamp = time.Now()
	c.cache[featureID] = data
}

// evictOldEntries removes expired entries (must be called with write lock held)
func (c *NumericFeatureCache[T]) evictOldEntries() {
	cutoff := time.Now().Add(-c.limits.MaxCacheAge)
	for featureID, data := range c.cache {
		if data.Timestamp.Before(cutoff) {
			delete(c.cache, featureID)
		}
	}
}

// Clear removes all cached data (useful for testing and memory management)
func (c *NumericFeatureCache[T]) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.cache = make(map[string]*PreSortedFeatureData[T])
}

// Size returns the number of cached features
func (c *NumericFeatureCache[T]) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return len(c.cache)
}
```

---
### File: internals/training/categorical.go
```go
package training

import (
	"fmt"

	"github.com/berrijam/mulberri/pkg/models"
)

// evaluateCategoricalSplit finds the best categorical split
func (c *C45Splitter[T]) evaluateCategoricalSplit(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	partitions := make(map[interface{}][]int)
	var errors MultiError

	// Group samples by their categorical values
	for _, sampleIndex := range dataset.GetIndices() {
		categoryValue, err := c.getCachedFeatureValue(dataset, sampleIndex, feature)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", sampleIndex, err))
			continue
		}
		partitions[categoryValue] = append(partitions[categoryValue], sampleIndex)
	}

	if errors.HasErrors() {
		return nil, &SplitError{Op: "evaluate_categorical", Feature: feature.Name, Err: &errors}
	}

	// Check if all values are missing (nil)
	if len(partitions) == 1 {
		for key := range partitions {
			if key == nil {
				return nil, &SplitError{
					Op:      "evaluate_categorical",
					Feature: feature.Name,
					Err:     fmt.Errorf("all feature values are missing"),
				}
			}
		}
	}

	// Remove partitions that are too small (below minimum leaf size)
	for key, indices := range partitions {
		if len(indices) < c.config.MinSamplesLeaf {
			delete(partitions, key)
		}
	}

	// If we end up with 0 or 1 partition, no meaningful split is possible
	if len(partitions) <= 1 {
		return nil, nil
	}

	total := dataset.GetSize()
	splitSizes := make([]int, 0, len(partitions))
	weightedImp := 0.0

	// Calculate weighted impurity for each partition
	for _, indices := range partitions {
		targetDist := c.getTargetDistFromPool()

		var partErrors MultiError
		for _, sampleIndex := range indices {
			target, err := dataset.GetTarget(sampleIndex)
			if err != nil {
				partErrors.Add(fmt.Errorf("sample %d: %w", sampleIndex, err))
				continue
			}
			targetDist[target]++
		}

		if partErrors.HasErrors() {
			c.returnTargetDistToPool(targetDist)
			return nil, &SplitError{Op: "evaluate_categorical_partition", Feature: feature.Name, Err: &partErrors}
		}

		partSize := len(indices)
		splitSizes = append(splitSizes, partSize)
		partImp := c.calculateDistributionImpurity(targetDist, partSize)
		weightedImp += (float64(partSize) / float64(total)) * partImp

		c.returnTargetDistToPool(targetDist)
	}

	// Calculate information gain and gain ratio
	infoGain := baseImpurity - weightedImp
	splitInfo := calculateSplitInfo(splitSizes, total)

	// Avoid division by zero or very small values
	if splitInfo <= baseFloatTolerance {
		return nil, nil
	}

	return &SplitResult[T]{
		Feature:    feature,
		InfoGain:   infoGain,
		GainRatio:  infoGain / splitInfo,
		Partitions: partitions,
	}, nil
}
```

---
### File: internals/training/config.go
```go
package training

import (
	"context"
	"runtime"
	"time"

	"github.com/berrijam/mulberri/cmd/mulberri/cli"
)

// Default resource limits
const (
	defaultMaxFeatures     = 10000
	defaultMaxDatasetSize  = 10000000
	defaultMaxCacheSize    = 5000000
	defaultTimeout         = 60 * time.Second
	defaultWorkerTimeout   = 100 * time.Millisecond
	defaultDrainTimeout    = 200 * time.Millisecond
	minFeaturesForParallel = 4

	// Adaptive timeout configuration
	minWorkerTimeout   = 50 * time.Millisecond // Minimum timeout regardless of context
	maxWorkerTimeout   = 5 * time.Second       // Maximum timeout to prevent indefinite blocking
	workerTimeoutRatio = 0.05                  // 5% of remaining context time
)

// ResourceLimits prevents resource exhaustion attacks
type ResourceLimits struct {
	MaxFeatures      int
	MaxDatasetSize   int
	MaxCacheSize     int
	MaxCacheAge      time.Duration
	Timeout          time.Duration
	WorkerTimeout    time.Duration // Timeout for worker goroutines when context is cancelled
	DrainTimeout     time.Duration // Timeout for draining results when exiting early
	AdaptiveTimeouts bool          // Whether to use adaptive timeouts based on context
}

func DefaultResourceLimits() ResourceLimits {
	return ResourceLimits{
		MaxFeatures:      defaultMaxFeatures,
		MaxDatasetSize:   defaultMaxDatasetSize,
		MaxCacheSize:     defaultMaxCacheSize,
		MaxCacheAge:      time.Hour,
		Timeout:          defaultTimeout,
		WorkerTimeout:    defaultWorkerTimeout,
		DrainTimeout:     defaultDrainTimeout,
		AdaptiveTimeouts: true, // Enable adaptive timeouts by default
	}
}

// SplitterConfig contains configuration options
type SplitterConfig struct {
	MinSamplesSplit int
	MinSamplesLeaf  int
	Criterion       ImpurityCriterion
	MaxWorkers      int
	CacheSize       int
	Limits          ResourceLimits
	EnableLogging   bool
}

// StringToImpurityCriterion converts a string criterion to ImpurityCriterion
func StringToImpurityCriterion(criterion string) ImpurityCriterion {
	switch criterion {
	case "gini":
		return GiniImpurity
	case "mse":
		return MSEImpurity
	case "entropy":
		return EntropyImpurity
	default:
		return EntropyImpurity
	}
}

func DefaultSplitterConfig() SplitterConfig {
	var cliConfig cli.Config

	criterion := StringToImpurityCriterion(cliConfig.Criterion)
	minSampleLeaf := max(cliConfig.MinSamplesLeaf, 1)
	minSampleSplit := max(cliConfig.MinSamplesSplit, 2)

	return SplitterConfig{
		MinSamplesSplit: minSampleSplit,
		MinSamplesLeaf:  minSampleLeaf,
		Criterion:       criterion,
		MaxWorkers:      runtime.NumCPU(),
		CacheSize:       1000,
		Limits:          DefaultResourceLimits(),
		EnableLogging:   false,
	}
}

// Functional options
type SplitterOption func(*SplitterConfig) error

func WithMinSamplesSplit(samples int) SplitterOption {
	return func(config *SplitterConfig) error {
		if samples < 2 {
			return &ValidationError{Field: "MinSamplesSplit", Value: samples, Reason: "must be >= 2"}
		}
		config.MinSamplesSplit = samples
		return nil
	}
}

func WithMinSamplesLeaf(samples int) SplitterOption {
	return func(config *SplitterConfig) error {
		if samples < 1 {
			return &ValidationError{Field: "MinSamplesLeaf", Value: samples, Reason: "must be >= 1"}
		}
		config.MinSamplesLeaf = samples
		return nil
	}
}

func WithImpurityCriterion(criterion ImpurityCriterion) SplitterOption {
	return func(config *SplitterConfig) error {
		if criterion < EntropyImpurity || criterion > MSEImpurity {
			return &ValidationError{Field: "Criterion", Value: criterion, Reason: "invalid impurity criterion"}
		}
		config.Criterion = criterion
		return nil
	}
}

func WithMaxWorkers(workers int) SplitterOption {
	return func(config *SplitterConfig) error {
		if workers < 1 {
			workers = runtime.NumCPU()
		}
		if workers > 64 {
			workers = 64
		}
		config.MaxWorkers = workers
		return nil
	}
}

func WithCacheSize(size int) SplitterOption {
	return func(config *SplitterConfig) error {
		if size < 0 {
			return &ValidationError{Field: "CacheSize", Value: size, Reason: "must be >= 0"}
		}
		config.CacheSize = size
		return nil
	}
}

func WithResourceLimits(limits ResourceLimits) SplitterOption {
	return func(config *SplitterConfig) error {
		if limits.MaxFeatures <= 0 || limits.MaxDatasetSize <= 0 {
			return &ValidationError{Field: "ResourceLimits", Reason: "limits must be positive"}
		}
		config.Limits = limits
		return nil
	}
}

func WithLogging(enabled bool) SplitterOption {
	return func(config *SplitterConfig) error {
		config.EnableLogging = enabled
		return nil
	}
}

func WithAdaptiveTimeouts(enabled bool) SplitterOption {
	return func(config *SplitterConfig) error {
		config.Limits.AdaptiveTimeouts = enabled
		return nil
	}
}

// CalculateAdaptiveTimeout calculates an appropriate timeout based on context and configuration
func (limits *ResourceLimits) CalculateAdaptiveTimeout(ctx context.Context, baseTimeout time.Duration) time.Duration {
	if !limits.AdaptiveTimeouts {
		return baseTimeout
	}

	// If context has a deadline, use a percentage of remaining time
	if deadline, ok := ctx.Deadline(); ok {
		remaining := time.Until(deadline)
		if remaining > 0 {
			adaptive := time.Duration(float64(remaining) * workerTimeoutRatio)

			// Apply bounds checking
			if adaptive < minWorkerTimeout {
				adaptive = minWorkerTimeout
			}
			if adaptive > maxWorkerTimeout {
				adaptive = maxWorkerTimeout
			}

			// Don't exceed the base timeout
			if adaptive > baseTimeout {
				adaptive = baseTimeout
			}

			return adaptive
		}
	}

	// Fallback to base timeout
	return baseTimeout
}
```

---
### File: internals/training/errors.go
```go
package training

import (
    "fmt"
    "strings"
)

// SplitError represents errors during splitting operations
type SplitError struct {
    Op      string
    Feature string
    Err     error
}

func (e *SplitError) Error() string {
    if e.Feature != "" {
        return fmt.Sprintf("split %s failed for feature '%s': %v", e.Op, e.Feature, e.Err)
    }
    return fmt.Sprintf("split %s failed: %v", e.Op, e.Err)
}

func (e *SplitError) Unwrap() error {
    return e.Err
}

// ValidationError represents input validation failures
type ValidationError struct {
    Field  string
    Value  interface{}
    Reason string
}

func (e *ValidationError) Error() string {
    return fmt.Sprintf("validation failed for %s (value: %v): %s", e.Field, e.Value, e.Reason)
}

// MultiError aggregates multiple errors for batch operations
type MultiError struct {
    Errors []error
}

func (e *MultiError) Error() string {
    if len(e.Errors) == 0 {
        return "no errors"
    }
    if len(e.Errors) == 1 {
        return e.Errors[0].Error()
    }
    
    var msgs []string
    for _, err := range e.Errors {
        msgs = append(msgs, err.Error())
    }
    return fmt.Sprintf("multiple errors (%d): %s", len(e.Errors), strings.Join(msgs, "; "))
}

func (e *MultiError) Add(err error) {
    if err != nil {
        e.Errors = append(e.Errors, err)
    }
}

func (e *MultiError) HasErrors() bool {
    return len(e.Errors) > 0
}
```

---
### File: internals/training/feature_creation.go
```go
// Package training implements decision tree training algorithms with enhanced error handling and validation.
package training

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// TrainingError represents errors that occur during training operations
type TrainingError struct {
	Op     string
	Field  string
	Index  int
	Reason string
	Err    error
}

func (e *TrainingError) Error() string {
	if e.Index >= 0 {
		return fmt.Sprintf("training %s error at index %d for field '%s': %s", e.Op, e.Index, e.Field, e.Reason)
	}
	if e.Field != "" {
		return fmt.Sprintf("training %s error for field '%s': %s", e.Op, e.Field, e.Reason)
	}
	return fmt.Sprintf("training %s error: %s", e.Op, e.Reason)
}

func (e *TrainingError) Unwrap() error {
	return e.Err
}

// FeatureCreationConfig holds configuration for feature creation
type FeatureCreationConfig struct {
	MinNumericSamples   int     // Minimum samples needed to consider a column numeric
	NumericThreshold    float64 // Minimum ratio of numeric values to consider column numeric
	TrimWhitespace      bool    // Whether to trim whitespace from values
	HandleMissingValues bool    // Whether to handle missing values gracefully
	ValidateInputs      bool    // Whether to validate input parameters
}

// DefaultFeatureCreationConfig returns a sensible default configuration
func DefaultFeatureCreationConfig() *FeatureCreationConfig {
	return &FeatureCreationConfig{
		MinNumericSamples:   2,
		NumericThreshold:    0.8,
		TrimWhitespace:      true,
		HandleMissingValues: true,
		ValidateInputs:      true,
	}
}

// validateFeatureCreationInputs validates input parameters for CreateFeatures
func validateFeatureCreationInputs(rows [][]string, headers []string, config *FeatureCreationConfig) error {
	if !config.ValidateInputs {
		return nil
	}

	if headers == nil {
		return &TrainingError{
			Op:     "validate_inputs",
			Field:  "headers",
			Reason: "headers cannot be nil",
		}
	}

	if rows == nil {
		return &TrainingError{
			Op:     "validate_inputs",
			Field:  "rows",
			Reason: "rows cannot be nil",
		}
	}

	// Validate header names
	for i, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}
		if header == "" {
			return &TrainingError{
				Op:     "validate_inputs",
				Field:  "headers",
				Index:  i,
				Reason: "header cannot be empty or whitespace-only",
			}
		}
	}

	// Check for duplicate headers
	headerMap := make(map[string]int)
	for i, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}
		if prevIndex, exists := headerMap[header]; exists {
			return &TrainingError{
				Op:     "validate_inputs",
				Field:  "headers",
				Index:  i,
				Reason: fmt.Sprintf("duplicate header '%s' found at indices %d and %d", header, prevIndex, i),
			}
		}
		headerMap[header] = i
	}

	return nil
}

/*
CreateFeatures performs feature categorization by analyzing raw feature data
and creating structured Feature objects suitable for decision tree algorithms.
This function examines each column of data to determine its type (numeric or categorical)
and creates appropriate Feature objects with comprehensive error handling.

Parameters:
  - rows: Feature data as a 2D slice (rows x columns) from ExtractFeatureAndTarget
  - headers: Names of the feature columns corresponding to each column in rows

Returns:
  - []*models.Feature: Array of Feature objects ready for machine learning algorithms
  - error: Error if validation fails or feature creation encounters issues
*/
func CreateFeatures(rows [][]string, headers []string) ([]*models.Feature, error) {
	return CreateFeaturesWithConfig(rows, headers, DefaultFeatureCreationConfig())
}

/*
CreateFeaturesFromMetadata creates features using feature info as the primary source.
This function prioritizes feature info definitions over automatic detection, providing
precise control over feature types and properties.

Parameters:
  - rows: Feature data as a 2D slice (rows x columns)
  - headers: Names of the feature columns
  - featureInfoConfig: Feature info configuration mapping feature names to their definitions
  - config: Configuration for feature creation behavior (optional, uses defaults if nil)

Returns:
  - []*models.Feature: Array of Feature objects created from feature info
  - error: Error if validation fails or feature creation encounters issues
*/
func CreateFeaturesFromMetadata(rows [][]string, headers []string, featureInfoConfig map[string]interface{}, config *FeatureCreationConfig) ([]*models.Feature, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	logger.Debug(fmt.Sprintf("Creating features from feature info: %d headers, %d rows, %d feature info entries", len(headers), len(rows), len(featureInfoConfig)))

	// Validate inputs
	if err := validateFeatureCreationInputs(rows, headers, config); err != nil {
		logger.Error(fmt.Sprintf("Input validation failed: %v", err))
		return nil, err
	}

	if len(headers) == 0 {
		logger.Debug("No headers provided, returning empty feature list")
		return []*models.Feature{}, nil
	}

	features := make([]*models.Feature, len(headers))
	for columnIndex, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}

		// Extract column values safely
		columnValues, err := extractColumnValues(rows, columnIndex, config)
		if err != nil {
			return nil, &TrainingError{
				Op:     "extract_column",
				Field:  "column_values",
				Index:  columnIndex,
				Reason: fmt.Sprintf("failed to extract column values: %v", err),
				Err:    err,
			}
		}

		// Check if feature info exists for this feature
		if featureInfo, exists := featureInfoConfig[header]; exists {
			logger.Debug(fmt.Sprintf("Creating feature '%s' from feature info at index %d", header, columnIndex))
			feature, err := createFeatureFromMetadata(header, columnIndex, columnValues, featureInfo, config)
			if err != nil {
				logger.Error(fmt.Sprintf("Failed to create feature '%s' from feature info: %v", header, err))
				return nil, &TrainingError{
					Op:     "create_feature_from_feature_info",
					Field:  "feature",
					Index:  columnIndex,
					Reason: fmt.Sprintf("failed to create feature '%s' from feature info: %v", header, err),
					Err:    err,
				}
			}
			features[columnIndex] = feature
		} else {
			// Fallback to automatic detection if no feature info found
			logger.Info(fmt.Sprintf("Feature '%s': no feature info provided, using automatic detection", header))
			feature, err := AnalyzeAndCreateFeatureWithConfig(header, columnIndex, columnValues, config)
			if err != nil {
				logger.Error(fmt.Sprintf("Failed to create feature '%s' with automatic detection: %v", header, err))
				return nil, &TrainingError{
					Op:     "create_feature_fallback",
					Field:  "feature",
					Index:  columnIndex,
					Reason: fmt.Sprintf("failed to create feature '%s' with automatic detection: %v", header, err),
					Err:    err,
				}
			}
			features[columnIndex] = feature
		}
	}

	logger.Debug(fmt.Sprintf("Successfully created %d features from feature info", len(features)))
	return features, nil
}

/*
CreateFeaturesWithConfig performs feature creation with custom configuration.
This allows fine-tuning of the feature creation process for specific use cases.

Parameters:
  - rows: Feature data as a 2D slice (rows x columns)
  - headers: Names of the feature columns
  - config: Configuration for feature creation behavior

Returns:
  - []*models.Feature: Array of Feature objects
  - error: Error if validation fails or feature creation encounters issues
*/
func CreateFeaturesWithConfig(rows [][]string, headers []string, config *FeatureCreationConfig) ([]*models.Feature, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	logger.Debug(fmt.Sprintf("Creating features: %d headers, %d rows", len(headers), len(rows)))

	// Validate inputs
	if err := validateFeatureCreationInputs(rows, headers, config); err != nil {
		logger.Debug(fmt.Sprintf("Input validation failed: %v", err))
		return nil, err
	}

	if len(headers) == 0 {
		logger.Debug("No headers provided, returning empty feature list")
		return []*models.Feature{}, nil
	}

	features := make([]*models.Feature, len(headers))
	for columnIndex, header := range headers {
		if config.TrimWhitespace {
			header = strings.TrimSpace(header)
		}

		// Extract column values safely
		columnValues, err := extractColumnValues(rows, columnIndex, config)
		if err != nil {
			return nil, &TrainingError{
				Op:     "extract_column",
				Field:  "column_values",
				Index:  columnIndex,
				Reason: fmt.Sprintf("failed to extract column values: %v", err),
				Err:    err,
			}
		}

		// Create feature with error handling
		logger.Debug(fmt.Sprintf("Creating feature '%s' at index %d", header, columnIndex))
		feature, err := AnalyzeAndCreateFeatureWithConfig(header, columnIndex, columnValues, config)
		if err != nil {
			logger.Error(fmt.Sprintf("Failed to create feature '%s': %v", header, err))
			return nil, &TrainingError{
				Op:     "create_feature",
				Field:  "feature",
				Index:  columnIndex,
				Reason: fmt.Sprintf("failed to create feature '%s': %v", header, err),
				Err:    err,
			}
		}

		logger.Debug(fmt.Sprintf("Successfully created feature '%s' (type: %v)", header, feature.Type))
		features[columnIndex] = feature
	}

	logger.Info(fmt.Sprintf("Successfully created %d features", len(features)))
	return features, nil
}

// extractColumnValues safely extracts values from a specific column
func extractColumnValues(rows [][]string, columnIndex int, config *FeatureCreationConfig) ([]string, error) {
	if columnIndex < 0 {
		return nil, fmt.Errorf("column index cannot be negative: %d", columnIndex)
	}

	columnValues := make([]string, len(rows))
	for rowIndex, row := range rows {
		var value string
		if columnIndex < len(row) {
			value = row[columnIndex]
			if config.TrimWhitespace {
				value = strings.TrimSpace(value)
			}
		} else if !config.HandleMissingValues {
			return nil, fmt.Errorf("row %d has %d columns, but column %d was requested", rowIndex, len(row), columnIndex)
		}
		// If HandleMissingValues is true, missing values become empty strings
		columnValues[rowIndex] = value
	}

	return columnValues, nil
}

/*
AnalyzeAndCreateFeature analyzes a single column of data and creates
the appropriate Feature object based on the detected data type.
Uses default configuration for backward compatibility.

Parameters:
  - name: Name of the feature (column header)
  - index: Column index in the original dataset
  - values: All values in this column as strings

Returns:
  - *models.Feature: Constructed feature object with appropriate type
  - error: Error if feature creation fails
*/
func AnalyzeAndCreateFeature(name string, index int, values []string) (*models.Feature, error) {
	return AnalyzeAndCreateFeatureWithConfig(name, index, values, DefaultFeatureCreationConfig())
}

/*
AnalyzeAndCreateFeatureWithConfig analyzes a single column of data and creates
the appropriate Feature object with custom configuration.

Parameters:
  - name: Name of the feature (column header)
  - index: Column index in the original dataset
  - values: All values in this column as strings
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Constructed feature object with appropriate type
  - error: Error if feature creation fails
*/
func AnalyzeAndCreateFeatureWithConfig(name string, index int, values []string, config *FeatureCreationConfig) (*models.Feature, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	// Validate inputs - always validate name and index as these are critical
	trimmedName := name
	if config.TrimWhitespace {
		trimmedName = strings.TrimSpace(name)
	}
	if trimmedName == "" {
		return nil, &TrainingError{
			Op:     "validate_feature_inputs",
			Field:  "name",
			Reason: "feature name cannot be empty or whitespace-only",
		}
	}

	if index < 0 {
		return nil, &TrainingError{
			Op:     "validate_feature_inputs",
			Field:  "index",
			Reason: fmt.Sprintf("feature index cannot be negative: %d", index),
		}
	}

	if config.ValidateInputs && values == nil {
		return nil, &TrainingError{
			Op:     "validate_feature_inputs",
			Field:  "values",
			Reason: "values cannot be nil",
		}
	}

	featureType, err := DetermineFeatureTypeWithConfig(values, config)
	if err != nil {
		return nil, &TrainingError{
			Op:     "determine_type",
			Field:  "feature_type",
			Reason: fmt.Sprintf("failed to determine feature type: %v", err),
			Err:    err,
		}
	}

	switch featureType {
	case models.NumericFeature:
		return CreateNumericFeatureWithConfig(trimmedName, index, values, config)
	case models.CategoricalFeature:
		return CreateCategoricalFeatureWithConfig(trimmedName, index, values, config)
	default:
		// Default to categorical for unknown types
		return CreateCategoricalFeatureWithConfig(trimmedName, index, values, config)
	}
}

/*
CreateNumericFeatureWithConfig creates a Feature object for numeric data with error handling.

Parameters:
  - name: Name of the feature
  - index: Column index in the dataset
  - values: String values from the column (will be converted to numbers)
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Numeric feature with min/max range set
  - error: Error if feature creation fails
*/
func CreateNumericFeatureWithConfig(name string, index int, values []string, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create feature with validation
	feature, err := models.NewFeature(name, models.NumericFeature, index)
	if err != nil {
		return nil, &TrainingError{
			Op:     "create_numeric_feature",
			Field:  "feature_creation",
			Reason: fmt.Sprintf("failed to create numeric feature: %v", err),
			Err:    err,
		}
	}

	var numValues []float64
	var parseErrors []string

	for i, valueString := range values {
		// Skip empty values
		if valueString == "" {
			continue
		}

		if num, parseErr := strconv.ParseFloat(valueString, 64); parseErr == nil {
			numValues = append(numValues, num)
		} else if config.ValidateInputs {
			parseErrors = append(parseErrors, fmt.Sprintf("row %d: '%s'", i, valueString))
		}
	}

	// Set range if we have numeric values
	if len(numValues) > 0 {
		min, max := numValues[0], numValues[0]
		for _, numericValue := range numValues[1:] {
			if numericValue < min {
				min = numericValue
			}
			if numericValue > max {
				max = numericValue
			}
		}

		// Only set range if min != max to avoid validation error
		if min != max {
			if err := feature.SetRange(min, max); err != nil {
				return nil, &TrainingError{
					Op:     "set_range",
					Field:  "numeric_range",
					Reason: fmt.Sprintf("failed to set numeric range [%f, %f]: %v", min, max, err),
					Err:    err,
				}
			}
		} else {
			// For single value, set a small range
			epsilon := 0.001
			if err := feature.SetRange(min-epsilon, max+epsilon); err != nil {
				return nil, &TrainingError{
					Op:     "set_range",
					Field:  "numeric_range",
					Reason: fmt.Sprintf("failed to set single-value range: %v", err),
					Err:    err,
				}
			}
		}
	} else if config.ValidateInputs && len(values) > 0 {
		return nil, &TrainingError{
			Op:     "parse_numeric_values",
			Field:  "values",
			Reason: fmt.Sprintf("no valid numeric values found, parse errors: %v", parseErrors),
		}
	}

	return feature, nil
}

/*
CreateCategoricalFeatureWithConfig creates a Feature object for categorical data with error handling.

Parameters:
  - name: Name of the feature
  - index: Column index in the dataset
  - values: String values from the column
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Categorical feature with unique values populated
  - error: Error if feature creation fails
*/
func CreateCategoricalFeatureWithConfig(name string, index int, values []string, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create feature with validation
	feature, err := models.NewFeature(name, models.CategoricalFeature, index)
	if err != nil {
		return nil, &TrainingError{
			Op:     "create_categorical_feature",
			Field:  "feature_creation",
			Reason: fmt.Sprintf("failed to create categorical feature: %v", err),
			Err:    err,
		}
	}

	// Collect unique non-empty values
	uniqueValues := make(map[string]bool)
	for _, categoryValue := range values {
		if config.TrimWhitespace {
			categoryValue = strings.TrimSpace(categoryValue)
		}
		// Skip empty values as they're not allowed by the models package
		if categoryValue != "" {
			uniqueValues[categoryValue] = true
		}
	}

	// Add unique values to feature
	for categoryValue := range uniqueValues {
		// Try the new AddCategoricalValue method first
		added, err := feature.AddCategoricalValue(categoryValue)
		if err != nil {
			// Fall back to legacy AddValue method
			_, legacyErr := feature.AddValue(categoryValue)
			if legacyErr != nil {
				return nil, &TrainingError{
					Op:     "add_categorical_value",
					Field:  "value",
					Reason: fmt.Sprintf("failed to add categorical value '%s': %v", categoryValue, legacyErr),
					Err:    legacyErr,
				}
			}
		} else if !added && config.ValidateInputs {
			// This is expected for duplicates, so only log in debug mode
			// Could add logging here if needed
		}
	}

	return feature, nil
}

/*
DetermineFeatureType analyzes a column of values to determine whether
it should be treated as numeric or categorical.
Uses default configuration for backward compatibility.

Parameters:
  - values: All string values from the column to analyze

Returns:
  - models.FeatureType: Either NumericFeature or CategoricalFeature
  - error: Error if analysis fails (only with validation enabled)
*/
func DetermineFeatureType(values []string) (models.FeatureType, error) {
	featureType, err := DetermineFeatureTypeWithConfig(values, DefaultFeatureCreationConfig())
	return featureType, err
}

/*
DetermineFeatureTypeWithConfig analyzes a column of values with custom configuration.

Parameters:
  - values: All string values from the column to analyze
  - config: Configuration for type determination

Returns:
  - models.FeatureType: Either NumericFeature or CategoricalFeature
  - error: Error if analysis fails
*/
func DetermineFeatureTypeWithConfig(values []string, config *FeatureCreationConfig) (models.FeatureType, error) {
	if config == nil {
		config = DefaultFeatureCreationConfig()
	}

	if config.ValidateInputs && values == nil {
		return models.CategoricalFeature, &TrainingError{
			Op:     "determine_type",
			Field:  "values",
			Reason: "values cannot be nil",
		}
	}

	if len(values) == 0 {
		return models.CategoricalFeature, nil
	}

	var numericCount, totalCount int

	for _, valueString := range values {
		if config.TrimWhitespace {
			valueString = strings.TrimSpace(valueString)
		}

		if valueString == "" {
			continue // Skip empty values
		}

		totalCount++
		if _, err := strconv.ParseFloat(valueString, 64); err == nil {
			numericCount++
		}
	}

	// If no non-empty values, default to categorical
	if totalCount == 0 {
		return models.CategoricalFeature, nil
	}

	// Check if we have enough numeric values
	if numericCount >= config.MinNumericSamples {
		numericRatio := float64(numericCount) / float64(totalCount)
		if numericRatio >= config.NumericThreshold {
			return models.NumericFeature, nil
		}
	}

	return models.CategoricalFeature, nil
}

/*
createFeatureFromMetadata creates a Feature object using metadata information.
This function interprets metadata to determine feature type and properties.

Parameters:
  - name: Name of the feature
  - index: Column index in the dataset
  - values: String values from the column
  - metadataInfo: Metadata information for this feature
  - config: Configuration for feature creation

Returns:
  - *models.Feature: Constructed feature object based on metadata
  - error: Error if feature creation fails
*/
func createFeatureFromMetadata(name string, index int, values []string, metadataInfo interface{}, config *FeatureCreationConfig) (*models.Feature, error) {
	// Convert metadata info to map for easier access
	metadataMap, ok := metadataInfo.(map[string]interface{})
	if !ok {
		return nil, &TrainingError{
			Op:     "parse_metadata",
			Field:  "metadata_info",
			Index:  index,
			Reason: fmt.Sprintf("metadata for feature '%s' is not a valid map", name),
		}
	}

	// Extract feature type from metadata
	featureTypeRaw, exists := metadataMap["type"]
	if !exists {
		return nil, &TrainingError{
			Op:     "parse_metadata",
			Field:  "type",
			Index:  index,
			Reason: fmt.Sprintf("metadata for feature '%s' missing 'type' field", name),
		}
	}

	featureTypeStr, ok := featureTypeRaw.(string)
	if !ok {
		return nil, &TrainingError{
			Op:     "parse_metadata",
			Field:  "type",
			Index:  index,
			Reason: fmt.Sprintf("metadata type for feature '%s' is not a string", name),
		}
	}

	// Convert metadata type to models.FeatureType
	var featureType models.FeatureType
	switch featureTypeStr {
	case "numeric":
		featureType = models.NumericFeature
	case "nominal":
		featureType = models.CategoricalFeature
	default:
		logger.Warn(fmt.Sprintf("Unknown metadata type '%s' for feature '%s', defaulting to categorical", featureTypeStr, name))
		featureType = models.CategoricalFeature
	}

	// Create feature based on type
	switch featureType {
	case models.NumericFeature:
		return createNumericFeatureFromMetadata(name, index, values, metadataMap, config)
	case models.CategoricalFeature:
		return createCategoricalFeatureFromMetadata(name, index, values, metadataMap, config)
	default:
		// Default to categorical for unknown types
		return createCategoricalFeatureFromMetadata(name, index, values, metadataMap, config)
	}
}

/*
createNumericFeatureFromMetadata creates a numeric feature using metadata constraints.
*/
func createNumericFeatureFromMetadata(name string, index int, values []string, metadataMap map[string]interface{}, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create the basic numeric feature first
	feature, err := CreateNumericFeatureWithConfig(name, index, values, config)
	if err != nil {
		return nil, err
	}

	// Apply metadata constraints if available
	var minVal, maxVal float64
	var hasMin, hasMax bool

	if minValInterface, exists := metadataMap["min"]; exists {
		if minFloat, ok := minValInterface.(float64); ok {
			minVal = minFloat
			hasMin = true
		}
	}

	if maxValInterface, exists := metadataMap["max"]; exists {
		if maxFloat, ok := maxValInterface.(float64); ok {
			maxVal = maxFloat
			hasMax = true
		}
	}

	// Set numeric range if constraints are provided
	if hasMin || hasMax {
		if !hasMin {
			minVal = 0 // Default minimum
		}
		if !hasMax {
			maxVal = 0 // Default maximum (will be ignored in validation)
		}

		err := feature.SetRange(minVal, maxVal)
		if err != nil {
			return nil, fmt.Errorf("failed to set numeric range for feature '%s': %w", name, err)
		}

		logger.Debug(fmt.Sprintf("Created numeric feature '%s' from metadata with range [%.2f, %.2f]", name, minVal, maxVal))
	} else {
		logger.Debug(fmt.Sprintf("Created numeric feature '%s' from metadata with no range constraints", name))
	}
	return feature, nil
}

/*
createCategoricalFeatureFromMetadata creates a categorical feature using metadata values.
*/
func createCategoricalFeatureFromMetadata(name string, index int, values []string, metadataMap map[string]interface{}, config *FeatureCreationConfig) (*models.Feature, error) {
	// Create the basic categorical feature first
	feature, err := CreateCategoricalFeatureWithConfig(name, index, values, config)
	if err != nil {
		return nil, err
	}

	// Apply metadata values if available
	if valuesRaw, exists := metadataMap["values"]; exists {
		if valuesSlice, ok := valuesRaw.([]interface{}); ok {
			// Convert interface{} slice to string slice
			metadataValues := make([]string, len(valuesSlice))
			for i, v := range valuesSlice {
				if str, ok := v.(string); ok {
					metadataValues[i] = str
				} else {
					metadataValues[i] = fmt.Sprintf("%v", v)
				}
			}
			feature.CategoricalValues = metadataValues
			logger.Debug(fmt.Sprintf("Created categorical feature '%s' from metadata with %d predefined values", name, len(metadataValues)))
		}
	}

	return feature, nil
}
```

---
### File: internals/training/impurity.go
```go
package training

import (
	"fmt"
	"math"
	"strconv"
)

// CalculateImpurity computes the impurity of a dataset using the configured criterion
func (c *C45Splitter[T]) CalculateImpurity(dataset Dataset[T]) (float64, error) {
	if dataset == nil {
		return 0, &ValidationError{Field: "dataset", Reason: "cannot be nil"}
	}

	n := dataset.GetSize()
	if n == 0 {
		return 0, nil
	}

	if c.config.Criterion == MSEImpurity {
		return c.calculateMSE(dataset)
	}

	return c.calculateClassificationImpurity(dataset)
}

// calculateClassificationImpurity calculates entropy or gini impurity
func (c *C45Splitter[T]) calculateClassificationImpurity(dataset Dataset[T]) (float64, error) {
	// Use pool for target distribution map
	dist := c.getTargetDistFromPool()
	defer c.returnTargetDistToPool(dist)

	var errors MultiError
	for _, idx := range dataset.GetIndices() {
		target, err := dataset.GetTarget(idx)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", idx, err))
			continue
		}
		dist[target]++
	}

	if errors.HasErrors() {
		return 0, &SplitError{Op: "calculate_impurity", Err: &errors}
	}

	n := dataset.GetSize()
	switch c.config.Criterion {
	case GiniImpurity:
		return calculateGini(dist, n), nil
	default:
		return calculateEntropy(dist, n), nil
	}
}

// calculateMSE computes mean squared error for regression
func (c *C45Splitter[T]) calculateMSE(dataset Dataset[T]) (float64, error) {
	indices := dataset.GetIndices()
	n := len(indices)
	if n == 0 {
		return 0, nil
	}

	var sum, sumSq float64
	var errors MultiError

	for _, idx := range indices {
		target, err := dataset.GetTarget(idx)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", idx, err))
			continue
		}

		x, err := c.convertToFloat64(target)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d target conversion: %w", idx, err))
			continue
		}

		sum += x
		sumSq += x * x
	}

	if errors.HasErrors() {
		return 0, &SplitError{Op: "calculate_mse", Err: &errors}
	}

	mean := sum / float64(n)
	return sumSq/float64(n) - mean*mean, nil
}

// calculateDistImpurity calculates impurity for a given distribution
func (c *C45Splitter[T]) calculateDistributionImpurity(dist map[T]int, size int) float64 {
	if size == 0 {
		return 0
	}

	switch c.config.Criterion {
	case GiniImpurity:
		return calculateGini(dist, size)
	default:
		return calculateEntropy(dist, size)
	}
}

// calculateEntropy computes Shannon entropy for a target distribution
// Entropy = -Σ(p_i * log2(p_i)) where p_i is the proportion of class i
func calculateEntropy[T comparable](dist map[T]int, total int) float64 {
	if total == 0 {
		return 0
	}
	entropy := 0.0
	for _, count := range dist {
		if count > 0 {
			p := float64(count) / float64(total)
			entropy -= p * math.Log2(p)
		}
	}
	return entropy
}

// calculateGini computes Gini impurity for a target distribution
// Gini = 1 - Σ(p_i^2) where p_i is the proportion of class i
func calculateGini[T comparable](dist map[T]int, total int) float64 {
	if total == 0 {
		return 0
	}
	gini := 1.0
	for _, count := range dist {
		p := float64(count) / float64(total)
		gini -= p * p
	}
	return gini
}

// calculateSplitInfo computes the split information used in gain ratio calculation
// SplitInfo = -Σ((|S_i|/|S|) * log2(|S_i|/|S|)) where S_i are the split partitions
func calculateSplitInfo(sizes []int, total int) float64 {
	if total == 0 {
		return 0
	}
	splitInfo := 0.0
	for _, size := range sizes {
		if size > 0 {
			ratio := float64(size) / float64(total)
			splitInfo -= ratio * math.Log2(ratio)
		}
	}
	return splitInfo
}

// convertToFloat64 safely converts various numeric types to float64
func (c *C45Splitter[T]) convertToFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("unsupported numeric type: %T", v)
	}
}
```

---
### File: internals/training/numeric.go
```go
package training

import (
	"fmt"
	"log"
	"sort"

	"github.com/berrijam/mulberri/pkg/models"
)

// evaluateNumericSplit finds the best threshold for a numeric feature
func (c *C45Splitter[T]) evaluateNumericSplit(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	// Check if we have pre-sorted data for this feature
	featureID := feature.Name
	if sortedData, found := c.numericCache.Get(featureID); found && sortedData.Sorted {
		// Filter values for current dataset indices
		values := c.filterSortedValues(sortedData.Values, dataset.GetIndices())
		if len(values) < c.config.MinSamplesSplit {
			return nil, nil
		}
		return c.findBestThreshold(values, baseImpurity, feature)
	}

	// Collect and sort values (first time for this feature)
	values, err := c.collectNumericValues(dataset, feature)
	if err != nil {
		return nil, &SplitError{Op: "collect_numeric_values", Feature: feature.Name, Err: err}
	}
	if len(values) < c.config.MinSamplesSplit {
		return nil, nil
	}

	sort.Slice(values, func(i, j int) bool {
		return values[i].Value < values[j].Value
	})

	// Cache the sorted data for future use
	c.numericCache.Set(featureID, &PreSortedFeatureData[T]{
		Feature: feature,
		Values:  values,
		Sorted:  true,
	})

	return c.findBestThreshold(values, baseImpurity, feature)
}

// filterSortedValues filters pre-sorted values based on current dataset indices
func (c *C45Splitter[T]) filterSortedValues(allValues []numericValue[T], indices []int) []numericValue[T] {
	// Create a set of indices for O(1) lookup
	indexSet := make(map[int]bool, len(indices))
	for _, sampleIndex := range indices {
		indexSet[sampleIndex] = true
	}

	result := make([]numericValue[T], 0, len(indices))
	for _, value := range allValues {
		if indexSet[value.Index] {
			result = append(result, value)
		}
	}
	return result
}

// collectNumericValues gathers numeric values from dataset
func (c *C45Splitter[T]) collectNumericValues(dataset Dataset[T], feature *models.Feature) ([]numericValue[T], error) {
	indices := dataset.GetIndices()
	values := make([]numericValue[T], 0, len(indices))
	var errors MultiError

	for _, sampleIndex := range indices {
		raw, err := c.getCachedFeatureValue(dataset, sampleIndex, feature)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d: %w", sampleIndex, err))
			continue
		}

		floatValue, err := c.convertToFloat64(raw)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d conversion: %w", sampleIndex, err))
			continue
		}

		target, err := dataset.GetTarget(sampleIndex)
		if err != nil {
			errors.Add(fmt.Errorf("sample %d target: %w", sampleIndex, err))
			continue
		}

		values = append(values, numericValue[T]{Value: floatValue, Index: sampleIndex, Target: target})
	}

	if errors.HasErrors() {
		return nil, &errors
	}

	return values, nil
}

// findBestThreshold finds the optimal threshold for numeric splitting
func (c *C45Splitter[T]) findBestThreshold(values []numericValue[T], baseImpurity float64, feature *models.Feature) (*SplitResult[T], error) {
	best := &SplitResult[T]{GainRatio: -1}

	// Use pools for distribution maps
	leftDist := c.getTargetDistFromPool()
	rightDist := c.getTargetDistFromPool()
	defer func() {
		c.returnTargetDistToPool(leftDist)
		c.returnTargetDistToPool(rightDist)
	}()

	// Initialize right distribution with all values
	for _, v := range values {
		rightDist[v.Target]++
	}

	total := len(values)
	for i := 0; i < total-1; i++ {
		// Move current value from right to left
		leftDist[values[i].Target]++
		rightDist[values[i].Target]--
		if rightDist[values[i].Target] == 0 {
			delete(rightDist, values[i].Target)
		}

		// Skip if next value is the same (no meaningful threshold)
		if floatEqual(values[i].Value, values[i+1].Value) {
			continue
		}

		leftSize := i + 1
		rightSize := total - leftSize

		// Check minimum samples constraints
		if leftSize < c.config.MinSamplesLeaf || rightSize < c.config.MinSamplesLeaf {
			continue
		}

		threshold, err := safeThreshold(values[i].Value, values[i+1].Value)
		if err != nil {
			if c.config.EnableLogging {
				log.Printf("Warning: threshold calculation failed for feature %s: %v", feature.Name, err)
			}
			continue
		}

		leftImp := c.calculateDistributionImpurity(leftDist, leftSize)
		rightImp := c.calculateDistributionImpurity(rightDist, rightSize)

		weightedImp := (float64(leftSize)/float64(total))*leftImp + (float64(rightSize)/float64(total))*rightImp
		infoGain := baseImpurity - weightedImp
		splitInfo := calculateSplitInfo([]int{leftSize, rightSize}, total)

		if splitInfo <= baseFloatTolerance {
			continue
		}

		gainRatio := infoGain / splitInfo
		if gainRatio > best.GainRatio {
			best = &SplitResult[T]{
				Feature:      feature,
				Threshold:    threshold,
				InfoGain:     infoGain,
				GainRatio:    gainRatio,
				LeftIndices:  getIndices(values, 0, i),
				RightIndices: getIndices(values, i+1, total-1),
			}
		}
	}

	if best.GainRatio <= 0 {
		return nil, nil
	}
	return best, nil
}

// getIndices extracts indices from a slice of numeric values
func getIndices[T comparable](values []numericValue[T], start, end int) []int {
	if start < 0 || end >= len(values) || start > end {
		return nil
	}

	indices := make([]int, 0, end-start+1)
	for i := start; i <= end; i++ {
		indices = append(indices, values[i].Index)
	}
	return indices
}
```

---
### File: internals/training/split.go
```go
package training

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// C45Splitter implements the C4.5 decision tree splitting algorithm with
// performance optimizations including feature value caching, pre-sorted
// numeric data, and parallel feature evaluation.
//
// The splitter uses gain ratio (information gain / split information) as
// the splitting criterion to avoid bias toward features with many values.
//
// Thread Safety: C45Splitter is safe for concurrent use. Internal caches
// use read-write mutexes and object pools are thread-safe.
type C45Splitter[T comparable] struct {
	config         SplitterConfig          // Configuration options
	featureCache   *FeatureValueCache[T]   // Cache for feature values
	numericCache   *NumericFeatureCache[T] // Cache for pre-sorted numeric data
	targetDistPool sync.Pool               // Pool for target distribution maps
	indicesPool    sync.Pool               // Pool for index slices
}

// NewC45Splitter creates a new C4.5 splitter with the given options
func NewC45Splitter[T comparable](options ...SplitterOption) (*C45Splitter[T], error) {
	config := DefaultSplitterConfig()

	// Apply all functional options
	for _, option := range options {
		if err := option(&config); err != nil {
			return nil, &SplitError{Op: "configure", Err: err}
		}
	}

	// Validate configuration consistency
	if config.MinSamplesLeaf > config.MinSamplesSplit {
		return nil, &SplitError{
			Op: "configure",
			Err: fmt.Errorf("MinSamplesLeaf (%d) cannot be greater than MinSamplesSplit (%d)",
				config.MinSamplesLeaf, config.MinSamplesSplit),
		}
	}

	splitter := &C45Splitter[T]{
		config:       config,
		featureCache: NewFeatureValueCache[T](config.Limits),
		numericCache: NewNumericFeatureCache[T](config.Limits),
	}

	// Initialize object pools with proper cleanup
	splitter.targetDistPool = sync.Pool{
		New: func() interface{} {
			return make(map[T]int)
		},
	}

	splitter.indicesPool = sync.Pool{
		New: func() interface{} {
			return make([]int, 0, 64)
		},
	}

	return splitter, nil
}

// FindBestSplit evaluates all provided features and returns the split that
// maximizes the gain ratio. Returns nil if no beneficial split is found.
//
// The algorithm:
//  1. Validates inputs and checks minimum sample requirements
//  2. Preloads feature values into cache for efficiency
//  3. Calculates base impurity of the current dataset
//  4. Evaluates each feature (parallel or sequential based on configuration)
//  5. Returns the split with the highest gain ratio
//
// For numeric features, the algorithm examines all possible binary splits
// by sorting values and testing thresholds between adjacent unique values.
//
// For categorical features, the algorithm creates a multi-way split with
// one branch per category value.
//
// Context cancellation is respected throughout the process.
func (c *C45Splitter[T]) FindBestSplit(ctx context.Context, dataset Dataset[T], features []*models.Feature) (*SplitResult[T], error) {
	if c.config.EnableLogging {
		logger.Debug(fmt.Sprintf("Starting split evaluation: %d samples, %d features", dataset.GetSize(), len(features)))
	}

	// Apply timeout if none specified
	if deadline, ok := ctx.Deadline(); !ok || time.Until(deadline) > c.config.Limits.Timeout {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, c.config.Limits.Timeout)
		defer cancel()
	}

	if err := c.validateInputs(dataset, features); err != nil {
		if c.config.EnableLogging {
			logger.Error( fmt.Sprintf("Input validation failed: %v", err))
		}
		return nil, err
	}

	if err := c.prepareForSplitting(ctx, dataset, features); err != nil {
		if c.config.EnableLogging {
			logger.Error( fmt.Sprintf("Preparation failed: %v", err))
		}
		return nil, err
	}

	baseImpurity, err := c.CalculateImpurity(dataset)
	if err != nil {
		if c.config.EnableLogging {
			logger.Error( fmt.Sprintf("Failed to calculate base impurity: %v", err))
		}
		return nil, &SplitError{Op: "calculate_base_impurity", Err: err}
	}

	if c.config.EnableLogging {
		logger.Debug( fmt.Sprintf("Base impurity calculated: %.6f", baseImpurity))
	}

	// If the dataset is pure (impurity = 0), no split is beneficial.
	// Returning (nil, nil) signals to the caller that this dataset should
	// become a leaf node since all samples have the same target value.
	if baseImpurity == 0 {
		if c.config.EnableLogging {
			logger.Debug( "Dataset is pure (impurity = 0), creating leaf node")
		}
		return nil, nil
	}

	result, err := c.executeSplitting(ctx, dataset, features, baseImpurity)
	if c.config.EnableLogging {
		if err != nil {
			logger.Error( fmt.Sprintf("Split execution failed: %v", err))
		} else if result != nil {
			logger.Debug( fmt.Sprintf("Best split found: feature=%s, gain_ratio=%.6f", result.Feature.Name, result.GainRatio))
		} else {
			logger.Debug( "No beneficial split found")
		}
	}
	return result, err
}

// validateInputs performs comprehensive input validation
func (c *C45Splitter[T]) validateInputs(dataset Dataset[T], features []*models.Feature) error {
	var errors MultiError

	// Basic nil checks
	if dataset == nil {
		errors.Add(&ValidationError{Field: "dataset", Reason: "cannot be nil"})
	} else {
		// Dataset-specific validation
		if dataset.GetSize() == 0 {
			errors.Add(&ValidationError{Field: "dataset", Reason: "cannot be empty"})
		} else if dataset.GetSize() > c.config.Limits.MaxDatasetSize {
			errors.Add(&ValidationError{
				Field:  "dataset",
				Value:  dataset.GetSize(),
				Reason: fmt.Sprintf("size exceeds limit of %d", c.config.Limits.MaxDatasetSize),
			})
		} else if dataset.GetSize() < c.config.MinSamplesSplit {
			errors.Add(&ValidationError{
				Field:  "dataset",
				Value:  dataset.GetSize(),
				Reason: fmt.Sprintf("size (%d) < min samples split (%d)", dataset.GetSize(), c.config.MinSamplesSplit),
			})
		}

		// Validate indices
		indices := dataset.GetIndices()
		if len(indices) == 0 {
			errors.Add(&ValidationError{Field: "dataset.indices", Reason: "no valid indices"})
		} else {
			for i, idx := range indices {
				if idx < 0 {
					errors.Add(&ValidationError{
						Field:  "dataset.indices",
						Value:  idx,
						Reason: fmt.Sprintf("negative index at position %d", i),
					})
				}
			}
		}
	}

	// Features validation
	if len(features) == 0 {
		errors.Add(&ValidationError{Field: "features", Reason: "cannot be empty"})
	} else if len(features) > c.config.Limits.MaxFeatures {
		errors.Add(&ValidationError{
			Field:  "features",
			Value:  len(features),
			Reason: fmt.Sprintf("count exceeds limit of %d", c.config.Limits.MaxFeatures),
		})
	} else {
		// Validate each feature
		featureNames := make(map[string]bool)
		for i, feature := range features {
			if feature == nil {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  i,
					Reason: fmt.Sprintf("feature at index %d is nil", i),
				})
				continue
			}

			// Check for duplicate names
			if featureNames[feature.Name] {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  feature.Name,
					Reason: "duplicate feature name",
				})
			}
			featureNames[feature.Name] = true

			// Validate feature name
			if feature.Name == "" {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  i,
					Reason: fmt.Sprintf("feature at index %d has empty name", i),
				})
			}

			// Validate feature type
			if !c.isValidFeatureType(feature.Type) {
				errors.Add(&ValidationError{
					Field:  "features",
					Value:  feature.Type,
					Reason: fmt.Sprintf("unsupported feature type for feature '%s'", feature.Name),
				})
			}
		}
	}

	if errors.HasErrors() {
		return &SplitError{Op: "validate", Err: &errors}
	}
	return nil
}

// isValidFeatureType checks if a feature type is supported
func (c *C45Splitter[T]) isValidFeatureType(featureType models.FeatureType) bool {
	switch featureType {
	case models.CategoricalFeature, models.NumericFeature, models.DateFeature:
		return true
	default:
		return false
	}
}

// prepareForSplitting handles pre-processing steps
func (c *C45Splitter[T]) prepareForSplitting(ctx context.Context, dataset Dataset[T], features []*models.Feature) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	return c.precacheFeatures(dataset, features)
}

// precacheFeatures loads feature values into cache for efficiency
func (c *C45Splitter[T]) precacheFeatures(dataset Dataset[T], features []*models.Feature) error {
	var errors MultiError

	for _, feature := range features {
		if err := c.featureCache.precacheFeature(dataset, feature); err != nil {
			if c.config.EnableLogging {
				logger.Warn(fmt.Sprintf("Failed to preload feature %s: %v", feature.Name, err))
			}
			errors.Add(fmt.Errorf("preload feature %s: %w", feature.Name, err))
		}
	}

	// Don't fail if some features can't be preloaded, just log warnings
	return nil
}

// executeSplitting performs the actual splitting logic
func (c *C45Splitter[T]) executeSplitting(ctx context.Context, dataset Dataset[T], features []*models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	if len(features) >= minFeaturesForParallel {
		return c.findBestSplitParallel(ctx, dataset, features, baseImpurity)
	}
	return c.findBestSplitSequential(ctx, dataset, features, baseImpurity)
}

// calculateOptimalWorkers determines the optimal number of workers
func (c *C45Splitter[T]) calculateOptimalWorkers(numFeatures int) int {
	maxWorkers := min(c.config.MaxWorkers, numFeatures)

	// For small numbers of features, sequential might be faster
	if numFeatures <= minFeaturesForParallel {
		return 1
	}

	return maxWorkers
}

// findBestSplitParallel uses parallel processing to evaluate features
func (c *C45Splitter[T]) findBestSplitParallel(ctx context.Context, dataset Dataset[T], features []*models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	numWorkers := c.calculateOptimalWorkers(len(features))
	numFeatures := len(features)

	jobs := make(chan *models.Feature, numFeatures)
	results := make(chan SplitWorkerResult[T], numFeatures)

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for feature := range jobs {
				select {
				case <-ctx.Done():
					// Use adaptive timeout to prevent blocking
					adaptiveTimeout := c.config.Limits.CalculateAdaptiveTimeout(ctx, c.config.Limits.WorkerTimeout)
					select {
					case results <- SplitWorkerResult[T]{Err: ctx.Err()}:
					case <-time.After(adaptiveTimeout):
					}
					return
				default:
				}

				split, err := c.safeEvaluateFeature(dataset, feature, baseImpurity)

				select {
				case results <- SplitWorkerResult[T]{Split: split, Err: err}:
				case <-ctx.Done():
					// Use adaptive timeout for draining results
					adaptiveTimeout := c.config.Limits.CalculateAdaptiveTimeout(ctx, c.config.Limits.DrainTimeout)
					select {
					case results <- SplitWorkerResult[T]{Err: ctx.Err()}:
					case <-time.After(adaptiveTimeout):
					}
					return
				}
			}
		}()
	}

	go func() {
		defer close(jobs)
		for _, feature := range features {
			select {
			case jobs <- feature:
			case <-ctx.Done():
				return
			}
		}
	}()

	go func() {
		wg.Wait()
		close(results)
	}()

	var bestSplit *SplitResult[T]
	var errors MultiError
	expectedResults := numFeatures
	collectedResults := 0

	timeout := time.After(c.config.Limits.Timeout)

	for collectedResults < expectedResults {
		select {
		case result, ok := <-results:
			if !ok {
				if c.config.EnableLogging {
					logger.Warn( fmt.Sprintf("Results channel closed early. Expected %d results, got %d", expectedResults, collectedResults))
				}
				break
			}

			collectedResults++

			if result.Err != nil {
				if c.config.EnableLogging {
					logger.Error( fmt.Sprintf("Feature evaluation error: %v", result.Err))
				}
				errors.Add(result.Err)
				continue
			}

			if result.Split != nil && (bestSplit == nil || result.Split.GainRatio > bestSplit.GainRatio) {
				bestSplit = result.Split
			}

		case <-ctx.Done():
			c.drainRemainingResults(results, &errors, &bestSplit, timeout)
			return bestSplit, ctx.Err()

		case <-timeout:
			// Overall timeout - collect what we have
			if c.config.EnableLogging {
				logger.Warn( fmt.Sprintf("Timeout waiting for results. Expected %d, got %d", expectedResults, collectedResults))
			}
			// Calculate drain timeout as 10% of main timeout with bounds checking.
			// This gives workers a reasonable amount of time to finish while preventing
			// indefinite blocking when the main timeout has already been exceeded.
			drainTimeout := time.Duration(float64(c.config.Limits.Timeout) * 0.1)
			if drainTimeout < c.config.Limits.WorkerTimeout {
				drainTimeout = c.config.Limits.WorkerTimeout
			}
			if drainTimeout > 2*time.Second {
				drainTimeout = 2 * time.Second
			}
			c.drainRemainingResults(results, &errors, &bestSplit, time.After(drainTimeout))

		}
	}

	// Handle final error state
	if errors.HasErrors() && bestSplit == nil {
		return nil, &SplitError{Op: "parallel_split", Err: &errors}
	}

	return bestSplit, nil
}

// Helper method to safely evaluate features with panic recovery
func (c *C45Splitter[T]) safeEvaluateFeature(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (split *SplitResult[T], err error) {
	defer func() {
		if r := recover(); r != nil {
			featureName := "unknown"
			if feature != nil {
				featureName = feature.Name
			}
			err = fmt.Errorf("panic during feature evaluation for feature '%s': %v", featureName, r)
			split = nil
		}
	}()

	return c.evaluateFeature(dataset, feature, baseImpurity)
}

// Helper method to drain remaining results when exiting early
func (c *C45Splitter[T]) drainRemainingResults(results <-chan SplitWorkerResult[T], errors *MultiError, bestSplit **SplitResult[T], timeout <-chan time.Time) {
	for {
		select {
		case result, ok := <-results:
			if !ok {
				return
			}

			if result.Err != nil {
				errors.Add(result.Err)
			} else if result.Split != nil && (*bestSplit == nil || result.Split.GainRatio > (*bestSplit).GainRatio) {
				*bestSplit = result.Split
			}

		case <-timeout:
			return
		}
	}
}

// evaluateFeature evaluates a single feature for splitting
func (c *C45Splitter[T]) evaluateFeature(dataset Dataset[T], feature *models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	switch feature.Type {
	case models.CategoricalFeature:
		return c.evaluateCategoricalSplit(dataset, feature, baseImpurity)
	case models.NumericFeature, models.DateFeature:
		return c.evaluateNumericSplit(dataset, feature, baseImpurity)
	default:
		return nil, &SplitError{
			Op:      "evaluate_feature",
			Feature: feature.Name,
			Err:     fmt.Errorf("unsupported feature type: %v", feature.Type),
		}
	}
}

// findBestSplitSequential processes features sequentially
func (c *C45Splitter[T]) findBestSplitSequential(ctx context.Context, dataset Dataset[T], features []*models.Feature, baseImpurity float64) (*SplitResult[T], error) {
	var bestSplit *SplitResult[T]
	var errors MultiError

	for _, feature := range features {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		split, err := c.evaluateFeature(dataset, feature, baseImpurity)
		if err != nil {
			if c.config.EnableLogging {
				logger.Error( fmt.Sprintf("Error evaluating feature %s: %v", feature.Name, err))
			}
			errors.Add(fmt.Errorf("feature %s: %w", feature.Name, err))
			continue
		}

		if split != nil && (bestSplit == nil || split.GainRatio > bestSplit.GainRatio) {
			bestSplit = split
		}
	}

	// If we have some results but also errors, log the errors but continue
	if errors.HasErrors() && bestSplit == nil {
		return nil, &SplitError{Op: "sequential_split", Err: &errors}
	}

	return bestSplit, nil
}

// Pool management functions with proper bounds checking
func (c *C45Splitter[T]) getTargetDistFromPool() map[T]int {
	return c.targetDistPool.Get().(map[T]int)
}

func (c *C45Splitter[T]) returnTargetDistToPool(dist map[T]int) {
	if len(dist) <= maxPooledMapSize {
		for k := range dist {
			delete(dist, k)
		}
		c.targetDistPool.Put(dist)
	}
}

// getCachedFeatureValue retrieves a cached feature value or fetches it
func (c *C45Splitter[T]) getCachedFeatureValue(dataset Dataset[T], idx int, feature *models.Feature) (interface{}, error) {
	// Add nil checks FIRST
	if dataset == nil {
		return nil, fmt.Errorf("dataset cannot be nil")
	}
	if feature == nil {
		return nil, fmt.Errorf("feature cannot be nil")
	}
	if feature.Name == "" {
		return nil, fmt.Errorf("feature name cannot be empty")
	}
	if idx < 0 {
		return nil, fmt.Errorf("index cannot be negative: %d", idx)
	}
	if c == nil {
		return nil, fmt.Errorf("splitter cannot be nil")
	}
	if c.featureCache == nil {
		return nil, fmt.Errorf("feature cache is not initialized")
	}
	if cached, found := c.featureCache.Get(feature.Name, idx); found {
		return cached, nil
	}

	value, err := dataset.GetFeatureValue(idx, feature)
	if err != nil {
		return nil, err
	}

	c.featureCache.Set(feature.Name, idx, value)
	return value, nil
}
```

---
### File: internals/training/types.go
```go
package training

import (
	"context"

	"github.com/berrijam/mulberri/pkg/models"
)

// Core constants
const (
    baseFloatTolerance     = 1e-10
    maxPooledSliceCapacity = 1024
    maxPooledMapSize       = 256
)

// ImpurityCriterion defines the method used to calculate node impurity
type ImpurityCriterion int

const (
    EntropyImpurity ImpurityCriterion = iota
    GiniImpurity
    MSEImpurity
)

// Core interfaces
type Splitter[T comparable] interface {
    FindBestSplit(ctx context.Context, dataset Dataset[T], features []*models.Feature) (*SplitResult[T], error)
    CalculateImpurity(dataset Dataset[T]) (float64, error)
}

type Dataset[T comparable] interface {
    GetSize() int
    GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error)
    GetTarget(sampleIdx int) (T, error)
    GetIndices() []int
    Subset(indices []int) Dataset[T]
}

// Core data types
type SplitResult[T comparable] struct {
    Feature      *models.Feature
    Threshold    float64
    InfoGain     float64
    GainRatio    float64
    LeftIndices  []int
    RightIndices []int
    Partitions   map[interface{}][]int
}

type numericValue[T comparable] struct {
    Value  float64
    Index  int
    Target T
}

type SplitWorkerResult[T comparable] struct {
    Split *SplitResult[T]
    Err   error
}
```

---
### File: internals/training/utils.go
```go
package training

import (
	"errors"
	"math"
)

// floatEqual checks if two float64 values are equal within the base tolerance
func floatEqual(a, b float64) bool {
	return math.Abs(a-b) < baseFloatTolerance
}

// safeThreshold calculates the midpoint between two values with overflow protection
func safeThreshold(a, b float64) (float64, error) {
	if math.IsInf(a, 0) || math.IsInf(b, 0) {
		return 0, errors.New("infinite values in threshold calculation")
	}
	return (a + b) / 2.0, nil
}
```

---
### File: internals/utils/csv_loader.go
```go
// Package utils provides utilities for loading and validating machine learning datasets.
//
// This package offers robust CSV loading with comprehensive validation, metadata parsing
// for feature type definitions, and utilities for data preprocessing. It emphasizes
// data integrity, clear error reporting, and performance for large datasets.
//
// Key features:
//   - RFC 4180 compliant CSV parsing with proper quote handling
//   - Comprehensive input validation with structured error types
//   - YAML-based feature metadata parsing with Unicode support
//   - Memory-efficient data processing with pre-allocation
//   - Configurable NA value detection and handling
//
// Basic usage for CSV loading:
//
//	headers, data, targetColumnIndex, err := utils.ReadTrainingCSV("data.csv", "target_column")
//	if err != nil {
//	    log.Fatal(err)
//	}
//
//	features, targets, featureHeaders, err := utils.ExtractFeatureAndTarget(
//	    data, headers, "target_column", targetColumnIndex)
//	if err != nil {
//	    log.Fatal(err)
//	}
//
// For metadata parsing:
//
//	parser := utils.NewFeatureInfoParser()
//	featureInfoConfig, err := parser.ParseFeatureInfoFile("features.yaml")
//	if err != nil {
//	    log.Fatal(err)
//	}
//
// Error handling:
//
//	All functions return structured error types that can be type-asserted for
//	specific error handling:
//
//	if csvErr, ok := err.(*utils.CSVError); ok {
//	    fmt.Printf("CSV error at line %d: %v
", csvErr.Line, csvErr.Err)
//	}
package utils

import (
	"errors"
	"fmt"
	"strings"
)

// Constants for configuration
const (
	MinRequiredDataRows = 2
	DefaultDelimiter    = ','
)

// Configurable NA values
var DefaultNAValues = []string{
	"na", "nan", "n/a", "null", "none",
	"missing", "unknown", "undefined",
}

// CSVError represents errors that occur during CSV processing
type CSVError struct {
	Op     string
	File   string
	Line   int
	Column string
	Err    error
}

func (e *CSVError) Error() string {
	if e.Line > 0 {
		if e.Column != "" {
			return fmt.Sprintf("CSV %s error in file '%s' at line %d, column '%s': %v", e.Op, e.File, e.Line, e.Column, e.Err)
		}
		return fmt.Sprintf("CSV %s error in file '%s' at line %d: %v", e.Op, e.File, e.Line, e.Err)
	}
	return fmt.Sprintf("CSV %s error in file '%s': %v", e.Op, e.File, e.Err)
}

func (e *CSVError) Unwrap() error {
	return e.Err
}

// ValidationError represents input validation failures
type ValidationError struct {
	Field  string
	Value  string
	Reason string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation failed for field '%s' with value '%s': %s", e.Field, e.Value, e.Reason)
}

// isNAValue checks if a string represents a common NA/NaN value
func isNAValue(s string) bool {
	// Handle empty string as NA
	if s == "" {
		return true
	}

	normalized := strings.ToLower(strings.TrimSpace(s))

	// Handle whitespace-only strings as NA
	if normalized == "" {
		return true
	}

	for _, naValue := range DefaultNAValues {
		if normalized == naValue {
			return true
		}
	}
	return false
}

// validateHeaders performs common header validation logic shared between CSV loaders
func validateHeaders(headers []string, filePath string) error {
	// Validate headers are not empty
	if len(headers) == 0 {
		return &CSVError{
			Op:   "validate_headers",
			File: filePath,
			Err:  errors.New("CSV file has no headers"),
		}
	}

	// Check for empty header fields, which could indicate quote parsing issues
	for i, h := range headers {
		if strings.TrimSpace(h) == "" {
			return &CSVError{
				Op:     "validate_headers",
				File:   filePath,
				Column: fmt.Sprintf("column_%d", i+1),
				Err:    fmt.Errorf("empty header field at column %d", i+1),
			}
		}
	}

	return nil
}
```

---
### File: internals/utils/dataset.go
```go
// Package utils provides dataset utilities for machine learning operations.
package utils

import (
	"fmt"
	"strconv"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// CSVDataset implements training.Dataset[string] for CSV data
type CSVDataset struct {
	features   [][]string
	targets    []string
	rowIndices []int // Row indices for dataset splitting and subset operations
}

// NewCSVDataset creates a new CSV dataset with sequential row indices
func NewCSVDataset(features [][]string, targets []string) training.Dataset[string] {
	if len(features) != len(targets) {
		errMsg := fmt.Sprintf("Features and targets length mismatch: %d vs %d", len(features), len(targets))
		logger.Fatal(errMsg)
	}

	sequentialRowIndices := make([]int, len(targets))
	for i := range sequentialRowIndices {
		sequentialRowIndices[i] = i
	}

	return &CSVDataset{
		features:   features,
		targets:    targets,
		rowIndices: sequentialRowIndices,
	}
}

// GetSize returns the number of samples in the current dataset
func (d *CSVDataset) GetSize() int {
	return len(d.rowIndices)
}

// GetFeatureValue retrieves a feature value for a given sample and feature
func (d *CSVDataset) GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.features) {
		return nil, fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.features))
	}

	columnIdx := feature.ColumnNumber
	if columnIdx >= len(d.features[sampleIdx]) {
		return nil, fmt.Errorf("feature column %d out of range for sample %d", columnIdx, sampleIdx)
	}

	value := d.features[sampleIdx][columnIdx]

	// Convert based on feature type
	switch feature.Type {
	case models.NumericFeature:
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue, nil
		}
		return value, nil
	case models.CategoricalFeature:
		return value, nil
	default:
		return value, nil
	}
}

// GetTarget retrieves the target value for a given sample
func (d *CSVDataset) GetTarget(sampleIdx int) (string, error) {
	if sampleIdx < 0 || sampleIdx >= len(d.targets) {
		return "", fmt.Errorf("sample index %d out of range [0, %d)", sampleIdx, len(d.targets))
	}

	return d.targets[sampleIdx], nil
}

// GetIndices returns the current row indices
func (d *CSVDataset) GetIndices() []int {
	return d.rowIndices
}

// Subset creates a new dataset view with the specified row indices
// This is a zero-copy operation that creates a new view without duplicating data
func (d *CSVDataset) Subset(subsetRowIndices []int) training.Dataset[string] {
	return &CSVDataset{
		features:   d.features,
		targets:    d.targets,
		rowIndices: subsetRowIndices,
	}
}
```

---
### File: internals/utils/feature_info_csv_loader.go
```go
// Package utils provides feature info CSV processing with enhanced validation.
//
// This module implements an iterative (non-batch) process for loading CSV row-by-row,
// validating elements according to feature metadata, and generating feature objects
// on-the-fly. This approach halves the memory required to load a dataset for training
// by avoiding the need to store the entire CSV in memory before processing.
package utils

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strings"

	"github.com/berrijam/mulberri/internals/training"
	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
)

// FeatureInfoCSVProcessor handles feature info CSV processing
type FeatureInfoCSVProcessor struct {
	filePath     string
	targetColumn string
	config       *FeatureInfoConfig
	features     []*models.Feature
	targets      []string
	featureData  [][]string // Store feature data for feature creation
	// Row indices are crucial for dataset splitting - they allow quick identification
	// and reference of records in different subsets when we split the dataset
	rowIndex []int
	lineNum  int
}

// FeatureInfoResult contains the results of feature info CSV processing
type FeatureInfoResult struct {
	Features       []*models.Feature
	Targets        []string
	FeatureHeaders []string
	FeatureData    [][]string
	// Row indices enable efficient dataset splitting and subset operations
	RowIndex    []int
	RecordCount int
}

// NewFeatureInfoCSVProcessor creates a new feature info processor
func NewFeatureInfoCSVProcessor(filePath, targetColumn string, config *FeatureInfoConfig) *FeatureInfoCSVProcessor {
	return &FeatureInfoCSVProcessor{
		filePath:     filePath,
		targetColumn: targetColumn,
		config:       config,
		features:     make([]*models.Feature, 0),
		targets:      make([]string, 0),
		featureData:  make([][]string, 0),
		rowIndex:     make([]int, 0),
		lineNum:      0,
	}
}

// ProcessWithFeatureInfo performs feature info CSV processing
func (p *FeatureInfoCSVProcessor) ProcessWithFeatureInfo() (*FeatureInfoResult, error) {
	logger.Info(fmt.Sprintf("Starting feature info CSV processing: file=%s, target=%s", p.filePath, p.targetColumn))

	// Open file for streaming
	file, err := os.Open(p.filePath)
	if err != nil {
		return nil, &CSVError{
			Op:   "open",
			File: p.filePath,
			Err:  err,
		}
	}
	defer file.Close()

	// Create CSV reader
	reader := csv.NewReader(bufio.NewReader(file))
	reader.FieldsPerRecord = -1
	reader.TrimLeadingSpace = true
	reader.LazyQuotes = false
	reader.Comma = DefaultDelimiter

	// Stage 1: Read and validate headers immediately
	headers, targetColumnIndex, err := p.readAndValidateHeaders(reader)
	if err != nil {
		return nil, err
	}

	// Stage 2: Create feature headers (excluding target)
	featureHeaders := p.createFeatureHeaders(headers, targetColumnIndex)

	// Stage 3: Set up streaming validation
	reader.FieldsPerRecord = len(headers)

	// Stage 4: Process records with incremental validation
	err = p.processRecordsIncrementally(reader, targetColumnIndex)
	if err != nil {
		return nil, err
	}

	// Stage 5: Create feature objects from processed data
	features, err := p.createFeaturesFromProcessedData(featureHeaders)
	if err != nil {
		return nil, err
	}

	result := &FeatureInfoResult{
		Features:       features,
		Targets:        p.targets,
		FeatureHeaders: featureHeaders,
		FeatureData:    p.featureData,
		RowIndex:       p.rowIndex,
		RecordCount:    len(p.targets),
	}

	logger.Info(fmt.Sprintf("Feature info processing completed: %d records, %d features", result.RecordCount, len(result.Features)))
	return result, nil
}

// readAndValidateHeaders reads headers and validates them against feature info
func (p *FeatureInfoCSVProcessor) readAndValidateHeaders(reader *csv.Reader) ([]string, int, error) {
	p.lineNum++
	headers, err := reader.Read()
	if err != nil {
		return nil, -1, &CSVError{
			Op:   "read_headers",
			File: p.filePath,
			Err:  err,
		}
	}

	// Validate headers using existing logic
	if err := validateHeaders(headers, p.filePath); err != nil {
		return nil, -1, err
	}

	// Find target column index
	targetColumnIndex := -1
	for i, h := range headers {
		if h == p.targetColumn {
			targetColumnIndex = i
			break
		}
	}

	if targetColumnIndex == -1 {
		return nil, -1, &ValidationError{
			Field:  "target_column",
			Value:  p.targetColumn,
			Reason: "not found in CSV headers",
		}
	}

	// Validate headers against feature info if provided
	if p.config != nil {
		err = p.validateHeadersAgainstConfig(headers)
		if err != nil {
			return nil, -1, err
		}
	}

	logger.Debug(fmt.Sprintf("Headers validated: %d columns, target='%s' at index %d", len(headers), p.targetColumn, targetColumnIndex))
	return headers, targetColumnIndex, nil
}

// validateHeadersAgainstConfig validates CSV headers against feature info
func (p *FeatureInfoCSVProcessor) validateHeadersAgainstConfig(headers []string) error {
	if p.config == nil {
		return nil
	}

	// Create header map for quick lookup
	headerMap := make(map[string]bool)
	for _, header := range headers {
		headerMap[header] = true
	}

	// Check if all configured features exist in CSV
	var missingFeatures []string
	for featureName := range *p.config {
		if !headerMap[featureName] {
			missingFeatures = append(missingFeatures, featureName)
		}
	}

	if len(missingFeatures) > 0 {
		return &ValidationError{
			Field:  "feature_config",
			Value:  strings.Join(missingFeatures, ", "),
			Reason: "features defined in feature info but missing from CSV headers",
		}
	}

	logger.Debug(fmt.Sprintf("Feature info validation passed: all %d configured features found in CSV", len(*p.config)))
	return nil
}

// createFeatureHeaders creates feature headers excluding target column
func (p *FeatureInfoCSVProcessor) createFeatureHeaders(headers []string, targetColumnIndex int) []string {
	featureHeaders := make([]string, 0, len(headers)-1)
	for i, h := range headers {
		if i != targetColumnIndex {
			featureHeaders = append(featureHeaders, h)
		}
	}
	return featureHeaders
}

// processRecordsIncrementally processes CSV records with incremental validation
func (p *FeatureInfoCSVProcessor) processRecordsIncrementally(reader *csv.Reader, targetColumnIndex int) error {
	recordCount := 0

	for {
		p.lineNum++
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return &CSVError{
				Op:   "parse_record",
				File: p.filePath,
				Line: p.lineNum,
				Err:  err,
			}
		}

		// Validate target column value
		if record[targetColumnIndex] == "" || isNAValue(record[targetColumnIndex]) {
			return &CSVError{
				Op:     "validate_target",
				File:   p.filePath,
				Line:   p.lineNum,
				Column: p.targetColumn,
				Err:    fmt.Errorf("empty or NA value in target column"),
			}
		}

		// Store target value
		p.targets = append(p.targets, record[targetColumnIndex])

		// Store row index for dataset splitting operations
		p.rowIndex = append(p.rowIndex, recordCount)

		// Extract and store feature data (excluding target column)
		featureRecord := make([]string, 0, len(record)-1)
		for j, cellValue := range record {
			if j != targetColumnIndex {
				featureRecord = append(featureRecord, cellValue)
			}
		}
		p.featureData = append(p.featureData, featureRecord)
		recordCount++
	}

	// Validate minimum record count
	if recordCount < MinRequiredDataRows {
		return &CSVError{
			Op:   "validate_data_size",
			File: p.filePath,
			Err:  fmt.Errorf("insufficient data rows (minimum %d required, got %d)", MinRequiredDataRows, recordCount),
		}
	}

	logger.Debug(fmt.Sprintf("Processed %d records with incremental validation", recordCount))
	return nil
}

// createFeaturesFromProcessedData creates feature objects from the processed and validated data
func (p *FeatureInfoCSVProcessor) createFeaturesFromProcessedData(featureHeaders []string) ([]*models.Feature, error) {
	logger.Debug(fmt.Sprintf("Creating features from processed data: %d headers, %d records", len(featureHeaders), len(p.featureData)))

	if p.config != nil {
		// Use feature info-based feature creation
		logger.Info(fmt.Sprintf("Creating features using provided feature info (%d configured features)", len(*p.config)))
		configMap := make(map[string]interface{})
		for name, info := range *p.config {
			configMap[name] = map[string]interface{}{
				"type":   info.Type,
				"values": info.Values,
				"min":    info.Min,
				"max":    info.Max,
			}
		}
		return training.CreateFeaturesFromMetadata(p.featureData, featureHeaders, configMap, nil)
	}

	// Fallback to automatic detection using collected feature data
	logger.Info("No feature info available - using automatic feature detection")
	logger.Info("Features will be automatically classified as numeric or categorical based on data analysis")
	return training.CreateFeatures(p.featureData, featureHeaders)
}
```

---
### File: internals/utils/feature_info.go
```go
package utils

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
)

// Maximum allowed file size for metadata files (10MB)
const MaxMetadataFileSize = 10 * 1024 * 1024

// validatePath performs security validation on file paths
func validatePath(filePath string) error {
	// Check for path traversal attempts
	cleanPath := filepath.Clean(filePath)

	// More thorough check for path traversal
	if strings.Contains(cleanPath, "..") || strings.Contains(filePath, "..") {
		return fmt.Errorf("path traversal detected: %s", filePath)
	}

	// Check for absolute path traversal on Windows
	if len(cleanPath) > 1 && cleanPath[1] == ':' {
		// Allow Windows absolute paths but validate they're reasonable
		if !filepath.IsAbs(cleanPath) {
			return fmt.Errorf("invalid Windows path format: %s", filePath)
		}
	}

	return nil
}

/*
ValidateFeatureInfoFile validates that a given file path points to a valid metadata file.
This function performs comprehensive validation including:
- Path validation (non-empty, properly formatted)
- File extension validation (must be .yaml or .yml)
- File existence and accessibility checks
- File type validation (must be a regular file, not a directory or special file)
- Read permission verification
- File size validation (prevents extremely large files)

Parameters:
  - filePath: The path to the file to validate (can be relative or absolute)

Returns:
  - string: The validated file path (cleaned and normalized)
  - error: An error describing what validation failed, or nil if validation passed
*/
func ValidateFeatureInfoFile(filePath string) (string, error) {
	// Log the start of validation
	logger.Debug(fmt.Sprintf("Validating feature info file: %s", filePath))

	// Validate the input path is not empty or whitespace-only
	trimmedPath := strings.TrimSpace(filePath)
	if trimmedPath == "" {
		logger.Error("Empty file path provided")
		return "", errors.New("empty file path provided")
	}

	// Security validation
	if err := validatePath(trimmedPath); err != nil {
		logger.Error(fmt.Sprintf("Path validation failed: %v", err))
		return "", fmt.Errorf("path validation failed: %w", err)
	}


	// Check if file exists and get file information
	info, err := os.Stat(trimmedPath)
	if err != nil {
		// Check if the error is specifically because the file doesn't exist
		if os.IsNotExist(err) {
			logger.Error(fmt.Sprintf("File does not exist: %s", trimmedPath))
			return "", fmt.Errorf("file does not exist: %s", trimmedPath)
		}
		logger.Error(fmt.Sprintf("Error accessing file: %v", err))
		return "", fmt.Errorf("error accessing file: %w", err)
	}

	// Verify it's a regular file
	if !info.Mode().IsRegular() {
		logger.Error(fmt.Sprintf("Not a regular file: %s", trimmedPath))
		return "", fmt.Errorf("not a regular file: %s", trimmedPath)
	}

	// Check file size to prevent extremely large files
	if info.Size() > MaxMetadataFileSize {
		logger.Error(fmt.Sprintf("File too large: %d bytes (max %d bytes)", info.Size(), MaxMetadataFileSize))
		return "", fmt.Errorf("file too large: %d bytes (maximum %d bytes allowed)", info.Size(), MaxMetadataFileSize)
	}

	// Check if file is readable
	file, err := os.Open(trimmedPath)
	if err != nil {
		logger.Error(fmt.Sprintf("Cannot read file: %v", err))
		return "", fmt.Errorf("cannot read file: %w", err)
	}
	file.Close()

	logger.Debug(fmt.Sprintf("Successfully validated feature info file: %s (%d bytes)", trimmedPath, info.Size()))
	return trimmedPath, nil
}
```

---
### File: internals/utils/feature_parser.go
```go
// Package utils provides utilities for parsing and validating machine learning feature information files.
package utils

import (
	"fmt"
	"os"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
	"github.com/berrijam/mulberri/pkg/models"
	"gopkg.in/yaml.v3"
)

// FeatureInfo represents information for a single feature from YAML.
// Supports nominal features (with values list) and numeric features (with optional min/max).
type FeatureInfo struct {
	Type   string   `yaml:"type"`             // "nominal" or "numeric"
	Values []string `yaml:"values,omitempty"` // Valid values for nominal features
	Min    *float64 `yaml:"min,omitempty"`    // Minimum value for numeric features
	Max    *float64 `yaml:"max,omitempty"`    // Maximum value for numeric features
}

// FeatureInfoParser handles parsing and validation of feature information files.
type FeatureInfoParser struct{}

// FeatureInfoConfig maps feature names to their information definitions.
type FeatureInfoConfig map[string]FeatureInfo

// FeatureInfoError represents errors that occur during feature info processing
type FeatureInfoError struct {
	Op      string
	File    string
	Feature string
	Err     error
}

func (e *FeatureInfoError) Error() string {
	if e.Feature != "" {
		return fmt.Sprintf("feature info %s error for feature '%s' in file '%s': %v", e.Op, e.Feature, e.File, e.Err)
	}
	return fmt.Sprintf("feature info %s error in file '%s': %v", e.Op, e.File, e.Err)
}

func (e *FeatureInfoError) Unwrap() error {
	return e.Err
}

// ToModelFeatureType converts YAML type to models.FeatureType.
// Returns CategoricalFeature for "nominal", NumericFeature for "numeric",
// and CategoricalFeature as default fallback.
func (fi *FeatureInfo) ToModelFeatureType() models.FeatureType {
	switch fi.Type {
	case "nominal":
		return models.CategoricalFeature
	case "numeric":
		return models.NumericFeature
	default:
		return models.CategoricalFeature
	}
}

// NewFeatureInfoParser creates a new parser.
func NewFeatureInfoParser() *FeatureInfoParser {
	return &FeatureInfoParser{}
}

// ParseFeatureInfoFile parses a YAML feature information file with graceful error handling.
// Returns (nil, nil) for missing files to enable fallback to automatic detection.
// Supports Unicode in feature names and values.
func (p *FeatureInfoParser) ParseFeatureInfoFile(filePath string) (*FeatureInfoConfig, error) {
	logger.Debug(fmt.Sprintf("Starting to parse feature info file: %s", filePath))

	// Validate file path first
	validatedPath, err := ValidateFeatureInfoFile(filePath)
	if err != nil {
		logger.Error(fmt.Sprintf("File validation failed: %v", err))
		return nil, &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  err,
		}
	}
	filePath = validatedPath

	// Read file with specific error context
	logger.Debug(fmt.Sprintf("Reading file: %s", filePath))
	data, err := os.ReadFile(filePath)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to read file: %v", err))
		return nil, &FeatureInfoError{
			Op:   "read",
			File: filePath,
			Err:  err,
		}
	}

	// Check for empty file
	if len(data) == 0 {
		logger.Error("File is empty")
		return nil, &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  fmt.Errorf("file is empty"),
		}
	}

	// Check for whitespace-only file
	if len(strings.TrimSpace(string(data))) == 0 {
		logger.Error("File contains only whitespace")
		return nil, &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  fmt.Errorf("file contains only whitespace"),
		}
	}

	logger.Debug(fmt.Sprintf("File size: %d bytes", len(data)))
	var config FeatureInfoConfig

	// Parse YAML with specific error context
	logger.Debug("Parsing YAML content")
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		logger.Debug(fmt.Sprintf("YAML parsing failed: %v", err))
		return nil, &FeatureInfoError{
			Op:   "parse_yaml",
			File: filePath,
			Err:  err,
		}
	}

	// Validate configuration
	logger.Debug(fmt.Sprintf("Validating configuration with %d features", len(config)))
	err = p.validateConfig(config, filePath)
	if err != nil {
		logger.Error(fmt.Sprintf("Configuration validation failed: %v", err))
		return nil, err // Already wrapped in validateConfig
	}

	logger.Info(fmt.Sprintf("Successfully parsed feature info file: %s (%d features)", filePath, len(config)))
	return &config, nil
}

// validateConfig ensures configuration contains at least one feature and validates each one.
func (p *FeatureInfoParser) validateConfig(config FeatureInfoConfig, filePath string) error {
	if len(config) == 0 {
		return &FeatureInfoError{
			Op:   "validate",
			File: filePath,
			Err:  fmt.Errorf("feature info file is empty or contains no features"),
		}
	}

	for featureName, featureInfo := range config {
		if err := p.validateFeature(featureName, featureInfo, filePath); err != nil {
			return err
		}
	}
	return nil
}

// validateFeature validates a single feature's information according to its type.
// Supports Unicode in feature names. Enforces type-specific constraints.
func (p *FeatureInfoParser) validateFeature(featureName string, featureInfo FeatureInfo, filePath string) error {
	// Validate feature name
	if strings.TrimSpace(featureName) == "" {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("feature name cannot be empty or whitespace-only"),
		}
	}

	// Validate type field
	if strings.TrimSpace(featureInfo.Type) == "" {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("must have a type field"),
		}
	}

	if featureInfo.Type != "nominal" && featureInfo.Type != "numeric" {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("invalid type '%s': must be 'nominal' or 'numeric'", featureInfo.Type),
		}
	}

	// Type-specific validation
	switch featureInfo.Type {
	case "nominal":
		return p.validateNominalFeature(featureName, featureInfo, filePath)
	case "numeric":
		return p.validateNumericFeature(featureName, featureInfo, filePath)
	}
	return nil
}

// validateNominalFeature validates nominal (categorical) feature requirements.
// Must have non-empty values list, no duplicates, no min/max properties.
func (p *FeatureInfoParser) validateNominalFeature(featureName string, featureInfo FeatureInfo, filePath string) error {
	if len(featureInfo.Values) == 0 {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("must have a non-empty values list"),
		}
	}

	// Check for empty values and duplicates
	valueSet := make(map[string]bool)
	for i, value := range featureInfo.Values {
		if strings.TrimSpace(value) == "" {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("value at index %d cannot be empty or whitespace-only", i),
			}
		}

		if valueSet[value] {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("duplicate value '%s' at index %d", value, i),
			}
		}
		valueSet[value] = true
	}

	// Nominal features should not have numeric constraints
	if featureInfo.Min != nil || featureInfo.Max != nil {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("should not have min or max properties"),
		}
	}

	return nil
}

// validateNumericFeature validates numeric (continuous) feature requirements.
// Cannot have values list. If min/max specified, min must be < max.
func (p *FeatureInfoParser) validateNumericFeature(featureName string, featureInfo FeatureInfo, filePath string) error {
	if len(featureInfo.Values) > 0 {
		return &FeatureInfoError{
			Op:      "validate",
			File:    filePath,
			Feature: featureName,
			Err:     fmt.Errorf("should not have a values list"),
		}
	}

	// Validate min/max relationship if both specified
	if featureInfo.Min != nil && featureInfo.Max != nil {
		if *featureInfo.Min > *featureInfo.Max {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min value (%.6f) cannot be greater than max value (%.6f)", *featureInfo.Min, *featureInfo.Max),
			}
		}
		if *featureInfo.Min == *featureInfo.Max {
			return &FeatureInfoError{
				Op:      "validate",
				File:    filePath,
				Feature: featureName,
				Err:     fmt.Errorf("min and max values cannot be equal (%.6f)", *featureInfo.Min),
			}
		}
	}

	return nil
}

// ValidateFeatureInfoAgainstCSV validates that feature info matches CSV headers and provides useful validation.
// This helps catch mismatches between feature info and actual data structure.
func (p *FeatureInfoParser) ValidateFeatureInfoAgainstCSV(config *FeatureInfoConfig, csvHeaders []string, filePath string) error {
	return p.ValidateFeatureInfoAgainstCSVWithTarget(config, csvHeaders, "", filePath)
}

// ValidateFeatureInfoAgainstCSVWithTarget validates that feature info matches CSV headers, excluding the target column.
// This helps catch mismatches between feature info and actual data structure while allowing target column feature info.
func (p *FeatureInfoParser) ValidateFeatureInfoAgainstCSVWithTarget(config *FeatureInfoConfig, csvHeaders []string, targetColumn string, filePath string) error {

	if config == nil {
		logger.Error("Feature config is nil")
		return &FeatureInfoError{
			Op:   "validate_csv",
			File: filePath,
			Err:  fmt.Errorf("feature config is nil"),
		}
	}
	logger.Debug(fmt.Sprintf("Validating feature config against CSV headers: %d features vs %d headers, target='%s'", len(*config), len(csvHeaders), targetColumn))

	// Create a map of CSV headers for quick lookup
	csvHeaderMap := make(map[string]bool)
	for _, header := range csvHeaders {
		csvHeaderMap[header] = true
	}

	// Add target column to the map if specified (so it's not considered missing)
	if targetColumn != "" {
		csvHeaderMap[targetColumn] = true
	}

	// Check if all features in config exist in CSV
	var missingInCSV []string
	for featureName := range *config {
		if !csvHeaderMap[featureName] {
			missingInCSV = append(missingInCSV, featureName)
		}
	}

	if len(missingInCSV) > 0 {
		logger.Error(fmt.Sprintf("Features missing in CSV: %v", missingInCSV))
		return &FeatureInfoError{
			Op:   "validate_csv",
			File: filePath,
			Err:  fmt.Errorf("features defined in feature info but missing in CSV: %v", missingInCSV),
		}
	}

	logger.Debug("Feature config validation against CSV successful")
	// Note: We don't require all CSV columns to be in feature info to allow for partial feature definitions
	return nil
}
```

---
### File: internals/utils/prediction_loader.go
```go
// Package utils provides utilities for loading prediction data for machine learning models.
package utils

import (
	"bufio"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"

	"github.com/berrijam/mulberri/pkg/models"
)

// PredictionRecord represents a single record for prediction with typed feature values
type PredictionRecord struct {
	Features map[string]interface{} `json:"features"`  // Feature name -> typed value
	RowIndex int                    `json:"row_index"` // Original row index for tracking
}

// ReadPredictionCSV reads and validates a CSV file containing prediction data (without target column).
// It parses feature values according to their expected types and returns structured records
// ready for prediction.
//
// The function performs the following validations:
// - File must be accessible for reading
// - CSV must have headers
// - Headers must not be empty strings
// - Each row must have the same number of fields as the header
// - There must be at least 1 data row (excluding header)
// - Properly handles quoted fields (e.g., "Hello, world" is treated as a single field)
//
// Note: File extension validation (.csv) is expected to be performed by the caller.
//
// Parameters:
// - filePath: Path to the CSV file containing prediction data
// - expectedFeatures: Map of feature names to their expected types for validation and parsing
//
// Returns:
// - []string: The headers from the CSV file
// - []PredictionRecord: Parsed and typed prediction records
// - error: Any error encountered during reading or validation
func ReadPredictionCSV(filePath string, expectedFeatures map[string]models.FeatureType) ([]string, []PredictionRecord, error) {
	// Validate input parameters
	if len(expectedFeatures) == 0 {
		return nil, nil, &ValidationError{
			Field:  "expected_features",
			Value:  "empty",
			Reason: "expected features map cannot be empty",
		}
	}

	// Open file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, nil, &CSVError{
			Op:   "open",
			File: filePath,
			Err:  err,
		}
	}
	defer file.Close()

	// Get file info for optimization
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, nil, &CSVError{
			Op:   "stat",
			File: filePath,
			Err:  err,
		}
	}

	// Estimate number of rows for pre-allocation. this reduces memory reallocation when reading the csv file But it can be resized as you append more files.
	estimatedRows := int(fileInfo.Size() / 100) // Rough estimate: 100 bytes per row
	if estimatedRows < 1 {
		estimatedRows = 10
	}

	// Process the CSV file with buffered reader for better performance
	reader := csv.NewReader(bufio.NewReader(file))

	// Configure the CSV reader
	reader.FieldsPerRecord = -1
	reader.TrimLeadingSpace = true
	reader.LazyQuotes = false // Enforce strict quotes (RFC 4180 compliant)
	reader.Comma = DefaultDelimiter

	// Read headers
	headers, err := reader.Read()
	if err != nil {
		return nil, nil, &CSVError{
			Op:   "read_headers",
			File: filePath,
			Err:  err,
		}
	}

	// Validate headers using shared validation logic
	if err := validateHeaders(headers, filePath); err != nil {
		return nil, nil, err
	}

	// Validate that all expected features are present in headers
	headerSet := make(map[string]int)
	for i, header := range headers {
		headerSet[header] = i
	}

	for featureName := range expectedFeatures {
		if _, exists := headerSet[featureName]; !exists {
			return nil, nil, &ValidationError{
				Field:  "missing_feature",
				Value:  featureName,
				Reason: "expected feature not found in CSV headers",
			}
		}
	}

	reader.FieldsPerRecord = len(headers)

	// Pre-allocate records slice with estimated size
	records := make([]PredictionRecord, 0, estimatedRows)
	// lineNum starts at 1 to account for the header row already read
	lineNum := 1

	for {
		lineNum++
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, nil, &CSVError{
				Op:   "parse_record",
				File: filePath,
				Line: lineNum,
				Err:  err,
			}
		}

		// Parse and validate the record
		predictionRecord, err := parsePredictionRecord(record, headers, expectedFeatures, lineNum)
		if err != nil {
			return nil, nil, &CSVError{
				Op:   "parse_prediction_record",
				File: filePath,
				Line: lineNum,
				Err:  err,
			}
		}

		predictionRecord.RowIndex = lineNum - 2 // Adjust for header and 0-based indexing
		records = append(records, predictionRecord)
	}

	// Check if we have any data
	if len(records) == 0 {
		return nil, nil, &CSVError{
			Op:   "validate_data_size",
			File: filePath,
			Err:  errors.New("no data rows found in CSV file"),
		}
	}

	return headers, records, nil
}

// parsePredictionRecord parses a single CSV record into a typed PredictionRecord
func parsePredictionRecord(record []string, headers []string, expectedFeatures map[string]models.FeatureType, lineNum int) (PredictionRecord, error) {
	features := make(map[string]interface{})

	for i, value := range record {
		if i >= len(headers) {
			return PredictionRecord{}, fmt.Errorf("record has more fields than headers")
		}

		featureName := headers[i]
		expectedType, isExpected := expectedFeatures[featureName]

		// Skip features that are not expected (e.g., extra columns in CSV)
		if !isExpected {
			continue
		}

		// Handle missing/NA values
		if value == "" || isNAValue(value) {
			// For missing values, we store nil and let the prediction engine handle it
			features[featureName] = nil
			continue
		}

		// Parse according to expected type
		switch expectedType {
		case models.NumericFeature:
			parsedValue, err := strconv.ParseFloat(strings.TrimSpace(value), 64)
			if err != nil {
				return PredictionRecord{}, fmt.Errorf("failed to parse numeric value '%s' for feature '%s': %w", value, featureName, err)
			}
			features[featureName] = parsedValue

		case models.CategoricalFeature:
			// Store categorical values as strings
			features[featureName] = strings.TrimSpace(value)

		case models.DateFeature:
			// For now, treat date features as strings
			// In the future, this could be enhanced to parse actual dates
			features[featureName] = strings.TrimSpace(value)

		default:
			return PredictionRecord{}, fmt.Errorf("unsupported feature type '%s' for feature '%s'", expectedType, featureName)
		}
	}

	return PredictionRecord{
		Features: features,
		RowIndex: 0, // Will be set by caller
	}, nil
}

// LoadPredictionDataFromModel is a convenience function that extracts feature schema from a trained model
// and loads prediction data accordingly.
//
// Parameters:
// - filePath: Path to the CSV file containing prediction data
// - model: Trained decision tree model containing feature schema
//
// Returns:
// - []string: The headers from the CSV file
// - []PredictionRecord: Parsed and typed prediction records
// - error: Any error encountered during reading or validation
func LoadPredictionDataFromModel(filePath string, model *models.DecisionTree) ([]string, []PredictionRecord, error) {
	// Basic safety checks to prevent panic and ensure model is usable
	if model == nil {
		return nil, nil, &ValidationError{
			Field:  "model",
			Value:  "nil",
			Reason: "model cannot be nil",
		}
	}

	if len(model.Features) == 0 {
		return nil, nil, &ValidationError{
			Field:  "model_features",
			Value:  "empty",
			Reason: "model must have features defined",
		}
	}

	// Extract feature types from model
	expectedFeatures := make(map[string]models.FeatureType)
	for name, feature := range model.Features {
		expectedFeatures[name] = feature.Type
	}

	return ReadPredictionCSV(filePath, expectedFeatures)
}
```

---
### File: internals/utils/prediction_writer.go
```go
package utils

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

// PredictionResult represents a single prediction result for output
type PredictionResult struct {
	Index            int                     `json:"index"`
	Prediction       interface{}             `json:"prediction"`
	Confidence       float64                 `json:"confidence"`
	Probabilities    map[interface{}]float64 `json:"probabilities,omitempty"`
	RulePath         string                  `json:"rule_path,omitempty"`
	DecisionPath     []string                `json:"decision_path,omitempty"`
	OriginalFeatures map[string]interface{}  `json:"original_features,omitempty"`
	ProcessingTime   time.Duration           `json:"processing_time,omitempty"`
	Error            string                  `json:"error,omitempty"`
}

// OutputFormat represents supported output formats
type OutputFormat string

const (
	CSVFormat  OutputFormat = "csv"
	JSONFormat OutputFormat = "json"
	TSVFormat  OutputFormat = "tsv"
)

// OutputConfig configures what data to include in output
type OutputConfig struct {
	IncludeIndex          bool
	IncludePrediction     bool
	IncludeConfidence     bool
	IncludeProbabilities  bool
	IncludeRulePath       bool
	IncludeDecisionPath   bool
	IncludeFeatures       bool
	IncludeProcessingTime bool
	IncludeErrors         bool
	SelectedFeatures      []string
}

// DefaultOutputConfig returns default configuration
func DefaultOutputConfig() OutputConfig {
	return OutputConfig{
		IncludeIndex:      true,
		IncludePrediction: true,
		IncludeConfidence: true,
		IncludeErrors:     true,
	}
}

// WritePredictionResults writes prediction results to a file
func WritePredictionResults(results []*PredictionResult, outputPath string, format OutputFormat, config OutputConfig) error {
	if len(results) == 0 {
		return fmt.Errorf("no results to write")
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	switch format {
	case CSVFormat:
		return writeCSV(results, outputPath, config)
	case TSVFormat:
		return writeTSV(results, outputPath, config)
	case JSONFormat:
		return writeJSON(results, outputPath, config)
	default:
		return fmt.Errorf("unsupported output format: %s", format)
	}
}

// writeCSV writes results in CSV format
func writeCSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return writeDelimited(results, outputPath, config, ',')
}

// writeTSV writes results in TSV format
func writeTSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return writeDelimited(results, outputPath, config, '	')
}

// writeDelimited writes results in delimited format (CSV/TSV)
func writeDelimited(results []*PredictionResult, outputPath string, config OutputConfig, delimiter rune) error {
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	writer.Comma = delimiter
	defer writer.Flush()

	// Write header
	headers := buildHeaders(results[0], config)
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("failed to write headers: %w", err)
	}

	// Write data rows
	for _, result := range results {
		row := buildRow(result, config)
		if err := writer.Write(row); err != nil {
			return fmt.Errorf("failed to write row: %w", err)
		}
	}

	return nil
}

// writeJSON writes results in JSON format
func writeJSON(results []*PredictionResult, outputPath string, config OutputConfig) error {
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")

	// Filter results based on config
	filteredResults := make([]map[string]interface{}, len(results))
	for i, result := range results {
		filteredResults[i] = filterResult(result, config)
	}

	return encoder.Encode(filteredResults)
}

// buildHeaders constructs CSV/TSV headers based on config
func buildHeaders(sample *PredictionResult, config OutputConfig) []string {
	var headers []string

	if config.IncludeIndex {
		headers = append(headers, "index")
	}
	if config.IncludePrediction {
		headers = append(headers, "prediction")
	}
	if config.IncludeConfidence {
		headers = append(headers, "confidence")
	}
	if config.IncludeProbabilities && len(sample.Probabilities) > 0 {
		// Sort probability keys for consistent ordering
		var classes []string
		for class := range sample.Probabilities {
			classes = append(classes, fmt.Sprintf("%v", class))
		}
		sort.Strings(classes)
		for _, class := range classes {
			headers = append(headers, "prob_"+class)
		}
	}
	if config.IncludeRulePath {
		headers = append(headers, "rule_path")
	}
	if config.IncludeDecisionPath {
		headers = append(headers, "decision_path")
	}
	if config.IncludeFeatures && len(sample.OriginalFeatures) > 0 {
		featureNames := getFeatureNames(sample.OriginalFeatures, config.SelectedFeatures)
		for _, name := range featureNames {
			headers = append(headers, "feature_"+name)
		}
	}
	if config.IncludeProcessingTime {
		headers = append(headers, "processing_time_ms")
	}
	if config.IncludeErrors {
		headers = append(headers, "error")
	}

	return headers
}

// buildRow constructs a CSV/TSV row based on config
func buildRow(result *PredictionResult, config OutputConfig) []string {
	var row []string

	if config.IncludeIndex {
		row = append(row, strconv.Itoa(result.Index))
	}
	if config.IncludePrediction {
		row = append(row, formatValue(result.Prediction))
	}
	if config.IncludeConfidence {
		row = append(row, formatFloat(result.Confidence))
	}
	if config.IncludeProbabilities && len(result.Probabilities) > 0 {
		// Sort probability keys for consistent ordering
		var classes []string
		for class := range result.Probabilities {
			classes = append(classes, fmt.Sprintf("%v", class))
		}
		sort.Strings(classes)
		for _, class := range classes {
			prob := 0.0
			for origClass, origProb := range result.Probabilities {
				if fmt.Sprintf("%v", origClass) == class {
					prob = origProb
					break
				}
			}
			row = append(row, formatFloat(prob))
		}
	}
	if config.IncludeRulePath {
		row = append(row, result.RulePath)
	}
	if config.IncludeDecisionPath {
		pathStr := strings.Join(result.DecisionPath, " | ")
		row = append(row, pathStr)
	}
	if config.IncludeFeatures && len(result.OriginalFeatures) > 0 {
		featureNames := getFeatureNames(result.OriginalFeatures, config.SelectedFeatures)
		for _, name := range featureNames {
			value := ""
			if val, exists := result.OriginalFeatures[name]; exists {
				value = formatValue(val)
			}
			row = append(row, value)
		}
	}
	if config.IncludeProcessingTime {
		timeMs := float64(result.ProcessingTime.Nanoseconds()) / 1000000.0
		row = append(row, formatFloat(timeMs))
	}
	if config.IncludeErrors {
		row = append(row, result.Error)
	}

	return row
}

// filterResult filters a result for JSON output based on config
func filterResult(result *PredictionResult, config OutputConfig) map[string]interface{} {
	filtered := make(map[string]interface{})

	if config.IncludeIndex {
		filtered["index"] = result.Index
	}
	if config.IncludePrediction {
		filtered["prediction"] = result.Prediction
	}
	if config.IncludeConfidence {
		filtered["confidence"] = result.Confidence
	}
	if config.IncludeProbabilities && len(result.Probabilities) > 0 {
		filtered["probabilities"] = result.Probabilities
	}
	if config.IncludeRulePath && result.RulePath != "" {
		filtered["rule_path"] = result.RulePath
	}
	if config.IncludeDecisionPath && len(result.DecisionPath) > 0 {
		filtered["decision_path"] = result.DecisionPath
	}
	if config.IncludeFeatures && len(result.OriginalFeatures) > 0 {
		if len(config.SelectedFeatures) > 0 {
			features := make(map[string]interface{})
			for _, name := range config.SelectedFeatures {
				if val, exists := result.OriginalFeatures[name]; exists {
					features[name] = val
				}
			}
			if len(features) > 0 {
				filtered["original_features"] = features
			}
		} else {
			filtered["original_features"] = result.OriginalFeatures
		}
	}
	if config.IncludeProcessingTime {
		timeMs := float64(result.ProcessingTime.Nanoseconds()) / 1000000.0
		filtered["processing_time_ms"] = timeMs
	}
	if config.IncludeErrors && result.Error != "" {
		filtered["error"] = result.Error
	}

	return filtered
}

// getFeatureNames returns sorted feature names for consistent column ordering
func getFeatureNames(features map[string]interface{}, selectedFeatures []string) []string {
	if len(selectedFeatures) > 0 {
		// Use only selected features in specified order
		var names []string
		for _, name := range selectedFeatures {
			if _, exists := features[name]; exists {
				names = append(names, name)
			}
		}
		return names
	}

	// Use all features in alphabetical order
	var names []string
	for name := range features {
		names = append(names, name)
	}
	sort.Strings(names)
	return names
}

// formatValue formats a value for CSV/TSV output
func formatValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case float64:
		return formatFloat(v)
	case float32:
		return formatFloat(float64(v))
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case bool:
		return strconv.FormatBool(v)
	case time.Time:
		return v.Format(time.RFC3339)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// formatFloat formats a float64 value with 6 decimal places
func formatFloat(value float64) string {
	return strconv.FormatFloat(value, 'f', 6, 64)
}

// ConvertTraversalResults converts traversal results to PredictionResult format
func ConvertTraversalResults(traversalResults []TraversalResultWithContext) []*PredictionResult {
	results := make([]*PredictionResult, len(traversalResults))

	for i, tr := range traversalResults {
		result := &PredictionResult{
			Index:            i,
			OriginalFeatures: tr.OriginalRecord,
			ProcessingTime:   tr.ProcessingTime,
		}

		if tr.Error != nil {
			result.Error = tr.Error.Error()
		} else if tr.Result != nil {
			result.Prediction = tr.Result.Prediction
			result.Confidence = tr.Result.Confidence
			result.Probabilities = tr.Result.Probabilities
			result.RulePath = tr.Result.RulePath
			result.DecisionPath = tr.Result.Path
		}

		results[i] = result
	}

	return results
}

// TraversalResultWithContext represents a traversal result with additional context
type TraversalResultWithContext struct {
	Result         *TraversalResult
	OriginalRecord map[string]interface{}
	ProcessingTime time.Duration
	Error          error
}

// TraversalResult represents the result of tree traversal
type TraversalResult struct {
	Prediction    interface{}
	Confidence    float64
	Probabilities map[interface{}]float64
	RulePath      string
	Path          []string
}

// WritePredictionCSV is a convenience function for CSV output
func WritePredictionCSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return WritePredictionResults(results, outputPath, CSVFormat, config)
}

// WritePredictionJSON is a convenience function for JSON output
func WritePredictionJSON(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return WritePredictionResults(results, outputPath, JSONFormat, config)
}

// WritePredictionTSV is a convenience function for TSV output
func WritePredictionTSV(results []*PredictionResult, outputPath string, config OutputConfig) error {
	return WritePredictionResults(results, outputPath, TSVFormat, config)
}
```

---
### File: pkg/logger/logger.go
```go
// Package logger provides a structured logging system for Mulberri with
// rotation, error tracking, and metadata support for decision tree operations.
// It includes both instance-based and global package-level logging functions.
// Uses zap for high-performance logging with environment-specific configurations.
package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"gopkg.in/yaml.v3"
)

// Default configuration constants for the logger.
const (
	DefaultLogFolder     = "logs"
	DefaultMaxSize       = 10
	DefaultEnableConsole = true
	DefaultAppName       = "mulberri"
	DefaultEnableColors  = true
	DefaultConfigPath    = "config/logger.yaml"
)

// Level represents the severity level of a log message (for backward compatibility).
type Level int

// Log levels in increasing order of severity (for backward compatibility).
const (
	LevelDebug Level = iota
	LevelInfo
	LevelWarning
	LevelError
	LevelFatal
)

// String returns the string representation of a log Level (for backward compatibility).
func (l Level) String() string {
	switch l {
	case LevelDebug:
		return "DEBUG  "
	case LevelInfo:
		return "INFO   "
	case LevelWarning:
		return "WARN   "
	case LevelError:
		return "ERROR  "
	case LevelFatal:
		return "FATAL  "
	default:
		return "UNKNOWN"
	}
}

// ParseLevel parses a string and returns the corresponding Level (for backward compatibility).
func ParseLevel(levelStr string) Level {
	switch strings.ToUpper(strings.TrimSpace(levelStr)) {
	case "DEBUG":
		return LevelDebug
	case "INFO":
		return LevelInfo
	case "WARN", "WARNING":
		return LevelWarning
	case "ERROR":
		return LevelError
	case "FATAL":
		return LevelFatal
	default:
		return LevelInfo
	}
}

// Environment represents the application environment
type Environment string

const (
	Development Environment = "development"
	Production  Environment = "production"
)

// Config holds logger configuration options.
type Config struct {
	LogFolder     string
	MaxSize       int64
	EnableConsole bool
	AppName       string
	EnableColors  bool
	Environment   Environment
	MaxBackups    int
	MaxAge        int
}

// yamlConfig is used for unmarshalling YAML configuration files.
type yamlConfig struct {
	Logger struct {
		LogFolder     string `yaml:"log_folder"`
		MaxSize       int64  `yaml:"max_size"`
		EnableConsole *bool  `yaml:"enable_console"`
		AppName       string `yaml:"app_name"`
		EnableColors  *bool  `yaml:"enable_colors"`
		Environment   string `yaml:"environment"`
		MaxBackups    int    `yaml:"max_backups"`
		MaxAge        int    `yaml:"max_age"`
	} `yaml:"logger"`
}

// Logger wraps zap.Logger to provide the same API as the original logger
type Logger struct {
	config    Config
	zapLogger *zap.Logger
	sugar     *zap.SugaredLogger
}

// Global logger instance and sync.Once for initialization.
var globalLogger *Logger
var globalOnce sync.Once
var globalMu sync.RWMutex

// getDefaultConfig returns a Config struct with default values.
func getDefaultConfig() Config {
	return Config{
		LogFolder:     DefaultLogFolder,
		MaxSize:       DefaultMaxSize,
		EnableConsole: DefaultEnableConsole,
		AppName:       DefaultAppName,
		EnableColors:  DefaultEnableColors,
		Environment:   Development,
		MaxBackups:    7,
		MaxAge:        5,
	}
}

// LoadConfigFromYAML loads logger configuration from a YAML file.
func LoadConfigFromYAML(configPath string) (Config, error) {
	if configPath == "" {
		configPath = DefaultConfigPath
	}
	config, err := loadConfigFromYAMLFile(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Warning: Failed to load logger config from %s: %v. Using defaults.
", configPath, err)
		return getDefaultConfig(), nil
	}
	return config, nil
}

// loadConfigFromYAMLFile reads and parses a YAML config file into Config.
func loadConfigFromYAMLFile(configPath string) (Config, error) {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return Config{}, fmt.Errorf("config file does not exist: %s", configPath)
	}
	data, err := os.ReadFile(configPath)
	if err != nil {
		return Config{}, fmt.Errorf("failed to read config file: %w", err)
	}
	var yamlCfg yamlConfig
	err = yaml.Unmarshal(data, &yamlCfg)
	if err != nil {
		return Config{}, fmt.Errorf("failed to parse YAML config: %w", err)
	}

	env := Development
	if yamlCfg.Logger.Environment == "production" {
		env = Production
	}

	return Config{
		LogFolder:     getStringOrDefault(yamlCfg.Logger.LogFolder, DefaultLogFolder),
		MaxSize:       getInt64OrDefault(yamlCfg.Logger.MaxSize, DefaultMaxSize),
		EnableConsole: getBoolOrDefault(yamlCfg.Logger.EnableConsole, DefaultEnableConsole),
		AppName:       getStringOrDefault(yamlCfg.Logger.AppName, DefaultAppName),
		EnableColors:  getBoolOrDefault(yamlCfg.Logger.EnableColors, DefaultEnableColors),
		Environment:   env,
		MaxBackups:    getIntOrDefault(yamlCfg.Logger.MaxBackups, 7),
		MaxAge:        getIntOrDefault(yamlCfg.Logger.MaxAge, 5),
	}, nil
}

// Helper functions for config parsing
func getStringOrDefault(value, defaultValue string) string {
	if value == "" {
		return defaultValue
	}
	return value
}

func getIntOrDefault(value, defaultValue int) int {
	if value < 0 {
		return defaultValue
	}
	return value
}
func getInt64OrDefault(value, defaultValue int64) int64 {
	if value <= 0 {
		return defaultValue
	}
	return value
}

func getBoolOrDefault(value *bool, defaultValue bool) bool {
	if value == nil {
		return defaultValue
	}
	return *value
}

// customConsoleEncoder creates a custom encoder that formats logs exactly as specified
type customConsoleEncoder struct {
	zapcore.Encoder
	enableColors bool
}

func newCustomConsoleEncoder() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: true}
}

func newCustomConsoleEncoderNoColors() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: false}
}

// customFileEncoder creates a custom encoder for file output (same format as console but no colors)
type customFileEncoder struct {
	zapcore.Encoder
}

func newCustomFileEncoder() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) Clone() zapcore.Encoder {
	return &customFileEncoder{}
}

func (enc *customFileEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("| ")

	// Level with proper spacing (file encoder - no colors)
	var levelStr string
	switch entry.Level {
	case zapcore.DebugLevel:
		levelStr = "DEBUG"
	case zapcore.InfoLevel:
		levelStr = "INFO "
	case zapcore.WarnLevel:
		levelStr = "WARN "
	case zapcore.ErrorLevel:
		levelStr = "ERROR"
	case zapcore.FatalLevel:
		levelStr = "FATAL"
	default:
		levelStr = "UNKNOWN"
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("
")

	return buf, nil
}

func (enc *customConsoleEncoder) Clone() zapcore.Encoder {
	return &customConsoleEncoder{enableColors: enc.enableColors}
}

func (enc *customConsoleEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	buf := buffer.NewPool().Get()

	// Time
	buf.AppendString("[36m")
	buf.AppendString(entry.Time.Format("2006-01-02 15:04:05"))
	buf.AppendString("[0m")
	buf.AppendString("| ")

	// Level with proper spacing and optional colors
	var levelStr string
	if enc.enableColors {
		// Add colors for different log levels
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "[35mDEBUG[0m"
		case zapcore.InfoLevel:
			levelStr = "[32mINFO [0m"
		case zapcore.WarnLevel:
			levelStr = "[33mWARN [0m"
		case zapcore.ErrorLevel:
			levelStr = "[31mERROR[0m"
		case zapcore.FatalLevel:
			levelStr = "[31mFATAL[0m"
		default:
			levelStr = "UNKNOWN"
		}
	} else {
		// No colors
		switch entry.Level {
		case zapcore.DebugLevel:
			levelStr = "DEBUG"
		case zapcore.InfoLevel:
			levelStr = "INFO "
		case zapcore.WarnLevel:
			levelStr = "WARN "
		case zapcore.ErrorLevel:
			levelStr = "ERROR"
		case zapcore.FatalLevel:
			levelStr = "FATAL"
		default:
			levelStr = "UNKNOWN"
		}
	}
	buf.AppendString(levelStr)
	buf.AppendString("|")

	// Caller info
	if entry.Caller.Defined {
		// Get the function name from the caller
		funcName := entry.Caller.Function
		if funcName != "" {
			// Extract just the function name (remove package path)
			if idx := strings.LastIndex(funcName, "."); idx >= 0 {
				funcName = funcName[idx+1:]
			}
		} else {
			funcName = "unknown"
		}

		// Get the file name (remove directory path)
		fileName := entry.Caller.File
		if idx := strings.LastIndex(fileName, "/"); idx >= 0 {
			fileName = fileName[idx+1:]
		}

		buf.AppendString(fmt.Sprintf("%s:%s:%d", fileName, funcName, entry.Caller.Line))
	}
	buf.AppendString(" | ")

	// Message
	buf.AppendString(entry.Message)
	buf.AppendString("
")

	return buf, nil
}

// createZapConfig creates a zap configuration based on environment and config
func createZapConfig(config Config) zap.Config {
	var zapConfig zap.Config

	if config.Environment == Production {
		// Production: JSON format, no caller info by default, structured
		zapConfig = zap.NewProductionConfig()
		zapConfig.DisableCaller = false // Enable caller info as requested
		zapConfig.DisableStacktrace = true
	} else {
		// Development: Console format, caller info, more verbose
		zapConfig = zap.NewDevelopmentConfig()
		zapConfig.DisableCaller = false
		zapConfig.DisableStacktrace = false
	}

	// Set encoding based on environment and color preference
	if config.Environment == Development && config.EnableColors {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else if config.Environment == Development {
		zapConfig.Encoding = "console"
		zapConfig.EncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	} else {
		zapConfig.Encoding = "json"
	}

	// Configure time format for file output (JSON)
	zapConfig.EncoderConfig.TimeKey = "timestamp"
	zapConfig.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05.000")
	zapConfig.EncoderConfig.CallerKey = "caller"
	zapConfig.EncoderConfig.EncodeCaller = zapcore.ShortCallerEncoder

	return zapConfig
}

// createLumberjackWriter creates a lumberjack writer for log rotation
func createLumberjackWriter(config Config) *lumberjack.Logger {
	currentDate := time.Now().Format("2006-01-02")
	logFile := filepath.Join(config.LogFolder, fmt.Sprintf("%s-%s.log", config.AppName, currentDate))
	return &lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    int(config.MaxSize), // MB
		MaxBackups: config.MaxBackups,   // Keep 30 backup files
		MaxAge:     config.MaxAge,       // Keep logs for 30 days
		Compress:   true,                // Compress old log files
	}
}

// NewWithConfig creates a new Logger with the given configuration.
func NewWithConfig(config Config) (*Logger, error) {
	// Create log directory
	if err := os.MkdirAll(config.LogFolder, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Create zap config
	zapConfig := createZapConfig(config)

	// Create cores for different outputs
	var cores []zapcore.Core

	// File output with rotation - use same format as console but without colors
	fileWriter := createLumberjackWriter(config)
	fileEncoder := newCustomFileEncoder() // Custom encoder without colors
	fileCore := zapcore.NewCore(fileEncoder, zapcore.AddSync(fileWriter), zapConfig.Level)
	cores = append(cores, fileCore)

	// Console output (if enabled)
	if config.EnableConsole {
		var consoleEncoder zapcore.Encoder
		if config.EnableColors {
			consoleEncoder = newCustomConsoleEncoder() // With colors
		} else {
			consoleEncoder = newCustomConsoleEncoderNoColors() // Without colors
		}
		consoleCore := zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapConfig.Level)
		cores = append(cores, consoleCore)
	}

	// Combine cores
	core := zapcore.NewTee(cores...)

	// Create logger with caller info
	zapLogger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(2))

	return &Logger{
		config:    config,
		zapLogger: zapLogger,
		sugar:     zapLogger.Sugar(),
	}, nil
}

// NewFromYAML creates a new Logger from a YAML configuration file.
func NewFromYAML(configPath string) (*Logger, error) {
	config, err := LoadConfigFromYAML(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	return NewWithConfig(config)
}


// Debug logs a debug-level message.
func (l *Logger) Debug(msg string) {
	l.sugar.Debug(msg)
}

// Info logs an info-level message.
func (l *Logger) Info(msg string) {
	l.sugar.Info(msg)
}

// Warn logs a warning-level message.
func (l *Logger) Warn(msg string) {
	l.sugar.Warn(msg)
}

// Error logs an error-level message.
func (l *Logger) Error(msg string) {
	l.sugar.Error(msg)
}

// Fatal logs a fatal-level message and exits.
func (l *Logger) Fatal(msg string) {
	l.sugar.Fatal(msg) // 
}

// Close closes the logger and syncs any buffered log entries.
func (l *Logger) Close() {
	if l.zapLogger != nil {
		l.zapLogger.Sync()
	}
}


// initGlobalLogger initializes the global logger with config from YAML.
func initGlobalLogger() {
	config, _ := LoadConfigFromYAML(DefaultConfigPath)
	initGlobalLoggerWithConfig(config)
}

// initGlobalLoggerWithConfig initializes the global logger with a given config.
func initGlobalLoggerWithConfig(config Config) {
	var err error
	globalLogger, err = NewWithConfig(config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize global logger: %v
", err)
		os.Exit(1)
	}
}

// getGlobalLogger returns the singleton global logger, initializing if needed.
func getGlobalLogger() *Logger {
	globalMu.RLock()
	if globalLogger != nil {
		defer globalMu.RUnlock()
		return globalLogger
	}
	globalMu.RUnlock()

	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger == nil {
		globalOnce.Do(initGlobalLogger)
	}
	return globalLogger
}

// Global convenience functions that maintain the original API
func Debug(msg string) { getGlobalLogger().Debug(msg) }
func Info(msg string)  { getGlobalLogger().Info(msg) }
func Warn(msg string)  { getGlobalLogger().Warn(msg) }
func Error(msg string) { getGlobalLogger().Error(msg) }
func Fatal(msg string) { getGlobalLogger().Fatal(msg) }

// SetGlobalConfig sets the global logger configuration.
func SetGlobalConfig(config Config) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}
	var err error
	globalLogger, err = NewWithConfig(config)
	return err
}

// SetGlobalConfigFromYAML sets the global logger config from a YAML file.
func SetGlobalConfigFromYAML(configPath string) error {
	globalMu.Lock()
	defer globalMu.Unlock()

	if globalLogger != nil {
		globalLogger.Close()
	}
	var err error
	globalLogger, err = NewFromYAML(configPath)
	return err
}

// CloseGlobal closes the global logger.
func CloseGlobal() {
	globalMu.Lock()
	defer globalMu.Unlock()
	if globalLogger != nil {
		globalLogger.Close()
		globalLogger = nil
	}
}
```

---
### File: pkg/models/feature.go
```go
// Package models contains core data structures for the decision tree implementation.
package models

import (
	"fmt"
	"strings"
	"time"
)

// FeatureType represents the type of a feature
type FeatureType string

const (
	// NumericFeature represents a numerical feature
	NumericFeature FeatureType = "numeric"
	// CategoricalFeature represents a categorical feature
	CategoricalFeature FeatureType = "categorical"
	// DateFeature represents a date feature
	DateFeature FeatureType = "date"
)

// ModelError represents errors in model operations
type ModelError struct {
	Op     string
	Field  string
	Value  string
	Reason string
	Err    error
}

func (e *ModelError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("model %s error for field '%s': %s", e.Op, e.Field, e.Reason)
	}
	return fmt.Sprintf("model %s error: %s", e.Op, e.Reason)
}

func (e *ModelError) Unwrap() error {
	return e.Err
}

// Range represents a numeric range with validation
type Range struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

// NewRange creates a validated numeric range
func NewRange(min, max float64) (*Range, error) {
	if min > max {
		return nil, &ModelError{
			Op:     "create_range",
			Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", min, max),
		}
	}
	if min == max {
		return nil, &ModelError{
			Op:     "create_range",
			Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", min),
		}
	}
	return &Range{Min: min, Max: max}, nil
}

// Contains checks if a value is within the range
func (r *Range) Contains(value float64) bool {
	return value >= r.Min && value <= r.Max
}

// DateRange represents a date range with validation
type DateRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// NewDateRange creates a validated date range
func NewDateRange(start, end time.Time) (*Range, error) {
	if start.After(end) {
		return nil, &ModelError{
			Op:     "create_date_range",
			Reason: "start date cannot be after end date",
		}
	}
	if start.Equal(end) {
		return nil, &ModelError{
			Op:     "create_date_range",
			Reason: "start and end dates cannot be equal",
		}
	}
	return &Range{
		Min: float64(start.Unix()),
		Max: float64(end.Unix()),
	}, nil
}

// Feature represents a feature in the dataset
type Feature struct {
	Name              string        `json:"name"`                         // Name of the feature (column name)
	Type              FeatureType   `json:"type"`                         // Type of the feature (numeric, categorical, date)
	ColumnNumber      int           `json:"column_number"`                // Column index (0-based) in the dataset
	Values            []interface{} `json:"values"`                       // Deprecated, use CategoricalValues
	CategoricalValues []string      `json:"categorical_values,omitempty"` // Valid values for categorical features
	NumericRange      *Range        `json:"numeric_range,omitempty"`      // Range constraints for numeric features
	DateRange         *DateRange    `json:"date_range,omitempty"`         // Range constraints for date features

	// Legacy fields for backward compatibility
	Min float64 `json:"min,omitempty"` // Min value (deprecated, use NumericRange)
	Max float64 `json:"max,omitempty"` // Max value (deprecated, use NumericRange)
}

// isValidFeatureType checks if a feature type is valid
func isValidFeatureType(featureType FeatureType) bool {
	switch featureType {
	case NumericFeature, CategoricalFeature, DateFeature:
		return true
	default:
		return false
	}
}

// NewFeature creates a new feature with validation
func NewFeature(name string, featureType FeatureType, columnNumber int) (*Feature, error) {
	// Validate inputs
	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		return nil, &ModelError{
			Op:     "create_feature",
			Field:  "name",
			Reason: "feature name cannot be empty or whitespace-only",
		}
	}

	if !isValidFeatureType(featureType) {
		return nil, &ModelError{
			Op:     "create_feature",
			Field:  "type",
			Value:  string(featureType),
			Reason: "invalid feature type, must be 'numeric', 'categorical', or 'date'",
		}
	}

	if columnNumber < 0 {
		return nil, &ModelError{
			Op:     "create_feature",
			Field:  "column_number",
			Value:  fmt.Sprintf("%d", columnNumber),
			Reason: "column number must be non-negative",
		}
	}

	feature := &Feature{
		Name:         trimmedName,
		Type:         featureType,
		ColumnNumber: columnNumber,
		Values:       make([]interface{}, 0), // For backward compatibility
	}

	// Initialize type-specific storage
	switch featureType {
	case CategoricalFeature:
		feature.CategoricalValues = make([]string, 0)
	}

	return feature, nil
}

// Validate performs comprehensive validation of the feature
func (f *Feature) Validate() error {
	if strings.TrimSpace(f.Name) == "" {
		return &ModelError{
			Op:     "validate_feature",
			Field:  "name",
			Reason: "name cannot be empty or whitespace-only",
		}
	}

	if f.ColumnNumber < 0 {
		return &ModelError{
			Op:     "validate_feature",
			Field:  "column_number",
			Value:  fmt.Sprintf("%d", f.ColumnNumber),
			Reason: "must be non-negative",
		}
	}

	switch f.Type {
	case CategoricalFeature:
		return f.validateCategorical()
	case NumericFeature:
		return f.validateNumeric()
	case DateFeature:
		return f.validateDate()
	default:
		return &ModelError{
			Op:     "validate_feature",
			Field:  "type",
			Value:  string(f.Type),
			Reason: "unknown feature type",
		}
	}
}

// validateCategorical validates categorical feature constraints
func (f *Feature) validateCategorical() error {
	// Check modern categorical values
	if len(f.CategoricalValues) > 0 {
		seen := make(map[string]bool)
		for i, value := range f.CategoricalValues {
			if strings.TrimSpace(value) == "" {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "categorical_values",
					Reason: fmt.Sprintf("value at index %d cannot be empty or whitespace-only", i),
				}
			}

			if seen[value] {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "categorical_values",
					Value:  value,
					Reason: fmt.Sprintf("duplicate value at index %d", i),
				}
			}
			seen[value] = true
		}
	}

	// Validate legacy Values field if present
	if len(f.Values) > 0 {
		seen := make(map[interface{}]bool)
		for i, value := range f.Values {
			if value == nil {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "values",
					Reason: fmt.Sprintf("value at index %d cannot be nil", i),
				}
			}

			// Check for empty strings
			if str, ok := value.(string); ok && strings.TrimSpace(str) == "" {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "values",
					Reason: fmt.Sprintf("string value at index %d cannot be empty", i),
				}
			}

			if seen[value] {
				return &ModelError{
					Op:     "validate_categorical",
					Field:  "values",
					Value:  fmt.Sprintf("%v", value),
					Reason: fmt.Sprintf("duplicate value at index %d", i),
				}
			}
			seen[value] = true
		}
	}

	return nil
}

// validateNumeric validates numeric feature constraints
func (f *Feature) validateNumeric() error {
	// Check modern range
	if f.NumericRange != nil {
		if f.NumericRange.Min > f.NumericRange.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "numeric_range",
				Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", f.NumericRange.Min, f.NumericRange.Max),
			}
		}
		if f.NumericRange.Min == f.NumericRange.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "numeric_range",
				Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", f.NumericRange.Min),
			}
		}
	}

	// Check legacy Min/Max fields
	if f.Min != 0 || f.Max != 0 {
		if f.Min > f.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "min_max_range",
				Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", f.Min, f.Max),
			}
		}
		if f.Min == f.Max {
			return &ModelError{
				Op:     "validate_numeric",
				Field:  "min_max_range",
				Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", f.Min),
			}
		}
	}

	return nil
}

// validateDate validates date feature constraints
func (f *Feature) validateDate() error {
	if f.DateRange != nil {
		if f.DateRange.Start.After(f.DateRange.End) {
			return &ModelError{
				Op:     "validate_date",
				Field:  "date_range",
				Reason: "start date cannot be after end date",
			}
		}
		if f.DateRange.Start.Equal(f.DateRange.End) {
			return &ModelError{
				Op:     "validate_date",
				Field:  "date_range",
				Reason: "start and end dates cannot be equal",
			}
		}
	}

	return nil
}

// SetRange sets the min and max values for numeric features with validation
func (f *Feature) SetRange(min, max float64) error {
	if f.Type != NumericFeature {
		return &ModelError{
			Op:     "set_range",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only set range for numeric features",
		}
	}

	if min > max {
		return &ModelError{
			Op:     "set_range",
			Reason: fmt.Sprintf("min value (%.6f) cannot be greater than max value (%.6f)", min, max),
		}
	}

	if min == max {
		return &ModelError{
			Op:     "set_range",
			Reason: fmt.Sprintf("min and max values cannot be equal (%.6f)", min),
		}
	}

	// Set both modern and legacy fields for compatibility
	range_, err := NewRange(min, max)
	if err != nil {
		return err
	}

	f.NumericRange = range_
	f.Min = min
	f.Max = max

	return nil
}

// AddValue adds a possible value for categorical features with validation
// Returns true if the value was added, false if it already existed or couldn't be added
func (f *Feature) AddValue(value interface{}) (bool, error) {
	// Only add values for categorical features
	if f.Type != CategoricalFeature {
		return false, &ModelError{
			Op:     "add_value",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only add values to categorical features",
		}
	}

	if value == nil {
		return false, &ModelError{
			Op:     "add_value",
			Field:  "value",
			Reason: "value cannot be nil",
		}
	}

	// Convert to string for modern storage
	strValue := fmt.Sprintf("%v", value)
	if strings.TrimSpace(strValue) == "" {
		return false, &ModelError{
			Op:     "add_value",
			Field:  "value",
			Value:  strValue,
			Reason: "value cannot be empty or whitespace-only",
		}
	}

	// Check if value already exists in modern storage
	for _, existing := range f.CategoricalValues {
		if existing == strValue {
			return false, nil // Already exists, not an error
		}
	}

	// Check if value already exists in legacy storage
	for _, existing := range f.Values {
		if existing == value {
			return false, nil // Already exists, not an error
		}
	}

	// Add to both modern and legacy storage for compatibility
	f.CategoricalValues = append(f.CategoricalValues, strValue)
	f.Values = append(f.Values, value)

	return true, nil
}

// AddCategoricalValue adds a string value specifically for categorical features
func (f *Feature) AddCategoricalValue(value string) (bool, error) {
	if f.Type != CategoricalFeature {
		return false, &ModelError{
			Op:     "add_categorical_value",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only add categorical values to categorical features",
		}
	}

	trimmedValue := strings.TrimSpace(value)
	if trimmedValue == "" {
		return false, &ModelError{
			Op:     "add_categorical_value",
			Field:  "value",
			Value:  value,
			Reason: "value cannot be empty or whitespace-only",
		}
	}

	// Check if value already exists
	for _, existing := range f.CategoricalValues {
		if existing == trimmedValue {
			return false, nil // Already exists, not an error
		}
	}

	f.CategoricalValues = append(f.CategoricalValues, trimmedValue)
	return true, nil
}

// GetCategoricalValues returns all categorical values for this feature
func (f *Feature) GetCategoricalValues() []string {
	if f.Type != CategoricalFeature {
		return nil
	}

	// Return copy to prevent external modification
	values := make([]string, len(f.CategoricalValues))
	copy(values, f.CategoricalValues)
	return values
}

// HasCategoricalValue checks if a categorical value exists
func (f *Feature) HasCategoricalValue(value string) bool {
	if f.Type != CategoricalFeature {
		return false
	}

	for _, existing := range f.CategoricalValues {
		if existing == value {
			return true
		}
	}
	return false
}

// GetValueCount returns the number of possible values for categorical features
func (f *Feature) GetValueCount() int {
	if f.Type != CategoricalFeature {
		return 0
	}
	return len(f.CategoricalValues)
}

// IsInRange checks if a numeric value is within the feature's range
func (f *Feature) IsInRange(value float64) (bool, error) {
	if f.Type != NumericFeature {
		return false, &ModelError{
			Op:     "check_range",
			Field:  "feature_type",
			Value:  string(f.Type),
			Reason: "can only check range for numeric features",
		}
	}

	// Check modern range first
	if f.NumericRange != nil {
		return f.NumericRange.Contains(value), nil
	}

	// Fall back to legacy range
	if f.Min != 0 || f.Max != 0 {
		return value >= f.Min && value <= f.Max, nil
	}

	// No range set, accept any value
	return true, nil
}
```

---
### File: pkg/models/tree.go
```go
// Package models contains core data structures for the decision tree implementation.
package models

import (
	"fmt"
	"math/rand"
	"strings"

	"github.com/berrijam/mulberri/pkg/logger"
)

// SplitCriterion defines the criteria used for splitting nodes
type SplitCriterion string

const (
	GiniCriterion    SplitCriterion = "gini"
	EntropyCriterion SplitCriterion = "entropy"
	MSECriterion     SplitCriterion = "mse" // Mean Squared Error for regression
)

// TreeConfig holds configuration for decision tree creation
type TreeConfig struct {
	MaxDepth    int            `json:"max_depth"`
	MinSamples  int            `json:"min_samples"`
	TargetType  FeatureType    `json:"target_type"`
	Criterion   SplitCriterion `json:"criterion"`
	RandomState *rand.Rand     `json:"-"` // Not serializable
	MaxFeatures int            `json:"max_features"`
}

// TreeOption defines functional options for tree configuration
type TreeOption func(*TreeConfig) error

// WithMaxDepth sets the maximum depth of the tree
func WithMaxDepth(depth int) TreeOption {
	return func(config *TreeConfig) error {
		if depth <= 0 {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "max_depth",
				Value:  fmt.Sprintf("%d", depth),
				Reason: "max depth must be positive",
			}
		}
		config.MaxDepth = depth
		return nil
	}
}

// WithMinSamples sets the minimum number of samples required to split
func WithMinSamples(samples int) TreeOption {
	return func(config *TreeConfig) error {
		if samples <= 0 {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "min_samples",
				Value:  fmt.Sprintf("%d", samples),
				Reason: "min samples must be positive",
			}
		}
		config.MinSamples = samples
		return nil
	}
}

// WithTargetType sets the target variable type
func WithTargetType(targetType FeatureType) TreeOption {
	return func(config *TreeConfig) error {
		if !isValidFeatureType(targetType) {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "target_type",
				Value:  string(targetType),
				Reason: "invalid target type",
			}
		}
		config.TargetType = targetType
		return nil
	}
}

// WithCriterion sets the split criterion
func WithCriterion(criterion SplitCriterion) TreeOption {
	return func(config *TreeConfig) error {
		switch criterion {
		case GiniCriterion, EntropyCriterion, MSECriterion:
			config.Criterion = criterion
			return nil
		default:
			return &ModelError{
				Op:     "configure_tree",
				Field:  "criterion",
				Value:  string(criterion),
				Reason: "invalid split criterion, must be 'gini', 'entropy', or 'mse'",
			}
		}
	}
}

// WithMaxFeatures sets the maximum number of features to consider for splits
func WithMaxFeatures(maxFeatures int) TreeOption {
	return func(config *TreeConfig) error {
		if maxFeatures <= 0 {
			return &ModelError{
				Op:     "configure_tree",
				Field:  "max_features",
				Value:  fmt.Sprintf("%d", maxFeatures),
				Reason: "max features must be positive",
			}
		}
		config.MaxFeatures = maxFeatures
		return nil
	}
}

// WithRandomState sets the random state for reproducible results
func WithRandomState(seed int64) TreeOption {
	return func(config *TreeConfig) error {
		config.RandomState = rand.New(rand.NewSource(seed))
		return nil
	}
}

// DecisionTree represents a decision tree model
type DecisionTree struct {
	Root            *TreeNode           `json:"root"`              // Root node of the tree
	Features        map[string]*Feature `json:"features"`          // Features used in the tree
	FeaturesByIndex []*Feature          `json:"features_by_index"` // Features indexed by column number
	TargetType      FeatureType         `json:"target_type"`       // Type of the target variable
	TargetColumn    string              `json:"target_column"`     // Name of the target column
	Config          TreeConfig          `json:"config"`            // Tree configuration

	// Tree statistics
	NodeCount int `json:"node_count"` // Total number of nodes
	LeafCount int `json:"leaf_count"` // Number of leaf nodes
	Depth     int `json:"depth"`      // Actual depth of the tree
}

// NewDecisionTree creates a new decision tree with basic parameters
func NewDecisionTree(targetType FeatureType, maxDepth, minSamples int) (*DecisionTree, error) {
	if !isValidFeatureType(targetType) {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "invalid target type",
		}
	}

	if maxDepth <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", maxDepth),
			Reason: "max depth must be positive",
		}
	}

	if minSamples <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "min_samples",
			Value:  fmt.Sprintf("%d", minSamples),
			Reason: "min samples must be positive",
		}
	}

	// Set default criterion based on target type
	criterion := GiniCriterion
	if targetType == NumericFeature {
		criterion = MSECriterion
	}

	return &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0),
		TargetType:      targetType,
		TargetColumn:    "", // Empty for backward compatibility
		Config: TreeConfig{
			MaxDepth:    maxDepth,
			MinSamples:  minSamples,
			TargetType:  targetType,
			Criterion:   criterion,
			MaxFeatures: -1, // Use all features by default
		},
		NodeCount: 0,
		LeafCount: 0,
		Depth:     0,
	}, nil
}

// NewDecisionTreeWithTarget creates a new decision tree with target column information
func NewDecisionTreeWithTarget(targetType FeatureType, targetColumn string, maxDepth, minSamples int) (*DecisionTree, error) {
	if !isValidFeatureType(targetType) {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "target_type",
			Value:  string(targetType),
			Reason: "invalid target type",
		}
	}

	if maxDepth <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", maxDepth),
			Reason: "max depth must be positive",
		}
	}

	if minSamples <= 0 {
		return nil, &ModelError{
			Op:     "create_tree",
			Field:  "min_samples",
			Value:  fmt.Sprintf("%d", minSamples),
			Reason: "min samples must be positive",
		}
	}

	// Set default criterion based on target type
	criterion := GiniCriterion
	if targetType == NumericFeature {
		criterion = MSECriterion
	}

	return &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0),
		TargetType:      targetType,
		TargetColumn:    targetColumn,
		Config: TreeConfig{
			MaxDepth:    maxDepth,
			MinSamples:  minSamples,
			TargetType:  targetType,
			Criterion:   criterion,
			MaxFeatures: -1, // Use all features by default
		},
		NodeCount: 0,
		LeafCount: 0,
		Depth:     0,
	}, nil
}

// NewDecisionTreeWithOptions creates a new decision tree using functional options
func NewDecisionTreeWithOptions(options ...TreeOption) (*DecisionTree, error) {
	// Default configuration
	config := &TreeConfig{
		MaxDepth:    10,
		MinSamples:  2,
		TargetType:  CategoricalFeature,
		Criterion:   GiniCriterion,
		MaxFeatures: -1,
	}

	// Apply options
	for _, option := range options {
		if err := option(config); err != nil {
			return nil, fmt.Errorf("invalid tree option: %w", err)
		}
	}

	tree := &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0),
		TargetType:      config.TargetType,
		TargetColumn:    "", // Will be set later if needed
		Config:          *config,
		NodeCount:       0,
		LeafCount:       0,
		Depth:           0,
	}

	return tree, nil
}

// SetTargetColumn sets the target column name for the tree
func (dt *DecisionTree) SetTargetColumn(targetColumn string) {
	dt.TargetColumn = targetColumn
}

// Validate performs comprehensive validation of the decision tree
func (dt *DecisionTree) Validate() error {
	if dt.Config.MaxDepth <= 0 {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "max_depth",
			Value:  fmt.Sprintf("%d", dt.Config.MaxDepth),
			Reason: "max depth must be positive",
		}
	}

	if dt.Config.MinSamples <= 0 {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "min_samples",
			Value:  fmt.Sprintf("%d", dt.Config.MinSamples),
			Reason: "min samples must be positive",
		}
	}

	if !isValidFeatureType(dt.TargetType) {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "target_type",
			Value:  string(dt.TargetType),
			Reason: "invalid target type",
		}
	}

	// Validate features
	for name, feature := range dt.Features {
		if err := feature.Validate(); err != nil {
			return &ModelError{
				Op:     "validate_tree_features",
				Field:  "feature",
				Value:  name,
				Reason: fmt.Sprintf("feature validation failed: %v", err),
				Err:    err,
			}
		}
	}

	// Validate feature index consistency
	if len(dt.FeaturesByIndex) != len(dt.Features) {
		return &ModelError{
			Op:     "validate_tree",
			Field:  "features_index",
			Reason: "feature count mismatch between map and index",
		}
	}

	// Validate tree structure if grown
	if dt.Root != nil {
		return dt.validateTreeStructure(dt.Root, 0)
	}

	return nil
}

// validateTreeStructure recursively validates the tree structure
func (dt *DecisionTree) validateTreeStructure(node *TreeNode, depth int) error {
	if node == nil {
		return nil
	}

	if depth > dt.Config.MaxDepth {
		return &ModelError{
			Op:     "validate_tree_structure",
			Field:  "depth",
			Value:  fmt.Sprintf("%d", depth),
			Reason: fmt.Sprintf("node depth exceeds max depth %d", dt.Config.MaxDepth),
		}
	}

	// Validate the node itself
	if err := node.Validate(); err != nil {
		return &ModelError{
			Op:     "validate_tree_structure",
			Field:  "node",
			Reason: fmt.Sprintf("node validation failed at depth %d: %v", depth, err),
			Err:    err,
		}
	}

	// Recursively validate children
	if !node.IsLeaf() {
		if node.Left != nil {
			if err := dt.validateTreeStructure(node.Left, depth+1); err != nil {
				return err
			}
		}

		if node.Right != nil {
			if err := dt.validateTreeStructure(node.Right, depth+1); err != nil {
				return err
			}
		}

		for category, child := range node.Categories {
			if child != nil {
				if err := dt.validateTreeStructure(child, depth+1); err != nil {
					return &ModelError{
						Op:     "validate_tree_structure",
						Field:  "category_child",
						Value:  fmt.Sprintf("%v", category),
						Reason: fmt.Sprintf("category child validation failed: %v", err),
						Err:    err,
					}
				}
			}
		}
	}

	return nil
}

// AddFeature adds a feature to the decision tree with validation
func (dt *DecisionTree) AddFeature(name string, featureType FeatureType) (*Feature, error) {
	logger.Debug(fmt.Sprintf("Adding feature: name='%s', type=%v", name, featureType))

	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		logger.Error("Feature name cannot be empty or whitespace-only")
		return nil, &ModelError{
			Op:     "add_feature",
			Field:  "name",
			Reason: "feature name cannot be empty or whitespace-only",
		}
	}

	// Check for duplicate names
	if _, exists := dt.Features[trimmedName]; exists {
		logger.Error(fmt.Sprintf("Feature with name '%s' already exists", trimmedName))
		return nil, &ModelError{
			Op:     "add_feature",
			Field:  "name",
			Value:  trimmedName,
			Reason: "feature with this name already exists",
		}
	}

	// Create feature with current index
	index := len(dt.Features)
	feature, err := NewFeature(trimmedName, featureType, index)
	if err != nil {
		logger.Error(fmt.Sprintf("Failed to create feature '%s': %v", trimmedName, err))
		return nil, &ModelError{
			Op:     "add_feature",
			Field:  "feature_creation",
			Reason: fmt.Sprintf("failed to create feature: %v", err),
			Err:    err,
		}
	}

	// Add to both storage methods
	dt.Features[trimmedName] = feature
	dt.FeaturesByIndex = append(dt.FeaturesByIndex, feature)

	logger.Debug(fmt.Sprintf("Successfully added feature '%s' at index %d", trimmedName, index))
	return feature, nil
}

// GetFeatureByName returns a feature by its name
func (dt *DecisionTree) GetFeatureByName(name string) *Feature {
	return dt.Features[name]
}

// GetFeatureByIndex returns a feature by its column index
func (dt *DecisionTree) GetFeatureByIndex(index int) (*Feature, error) {
	if index < 0 || index >= len(dt.FeaturesByIndex) {
		return nil, &ModelError{
			Op:     "get_feature_by_index",
			Field:  "index",
			Value:  fmt.Sprintf("%d", index),
			Reason: fmt.Sprintf("index out of range [0, %d)", len(dt.FeaturesByIndex)),
		}
	}
	return dt.FeaturesByIndex[index], nil
}

// GetFeatureCount returns the number of features in the tree
func (dt *DecisionTree) GetFeatureCount() int {
	return len(dt.Features)
}

// HasFeature checks if a feature exists in the tree
func (dt *DecisionTree) HasFeature(name string) bool {
	_, exists := dt.Features[name]
	return exists
}

// IsGrown checks if the tree has been trained (has a root node)
func (dt *DecisionTree) IsGrown() bool {
	return dt.Root != nil
}

// GetDepth returns the actual depth of the tree
func (dt *DecisionTree) GetDepth() int {
	if dt.Root == nil {
		return 0
	}
	return dt.calculateNodeDepth(dt.Root)
}

// calculateNodeDepth recursively calculates the depth of a subtree
func (dt *DecisionTree) calculateNodeDepth(node *TreeNode) int {
	if node == nil || node.IsLeaf() {
		return 1
	}

	maxChildDepth := 0

	// Check left and right children for numeric/date features
	if node.Left != nil {
		if depth := dt.calculateNodeDepth(node.Left); depth > maxChildDepth {
			maxChildDepth = depth
		}
	}
	if node.Right != nil {
		if depth := dt.calculateNodeDepth(node.Right); depth > maxChildDepth {
			maxChildDepth = depth
		}
	}

	// Check categorical children
	for _, child := range node.Categories {
		if child != nil {
			if depth := dt.calculateNodeDepth(child); depth > maxChildDepth {
				maxChildDepth = depth
			}
		}
	}

	return maxChildDepth + 1
}

// GetNodeCount returns the total number of nodes in the tree
func (dt *DecisionTree) GetNodeCount() int {
	if dt.Root == nil {
		return 0
	}
	return dt.countNodes(dt.Root)
}

// countNodes recursively counts all nodes in the tree
func (dt *DecisionTree) countNodes(node *TreeNode) int {
	if node == nil {
		return 0
	}

	count := 1 // Count this node

	// Count children
	if !node.IsLeaf() {
		count += dt.countNodes(node.Left)
		count += dt.countNodes(node.Right)

		for _, child := range node.Categories {
			count += dt.countNodes(child)
		}
	}

	return count
}

// GetLeafCount returns the number of leaf nodes in the tree
func (dt *DecisionTree) GetLeafCount() int {
	if dt.Root == nil {
		return 0
	}
	return dt.countLeaves(dt.Root)
}

// countLeaves recursively counts leaf nodes in the tree
func (dt *DecisionTree) countLeaves(node *TreeNode) int {
	if node == nil {
		return 0
	}

	if node.IsLeaf() {
		return 1
	}

	count := 0
	count += dt.countLeaves(node.Left)
	count += dt.countLeaves(node.Right)

	for _, child := range node.Categories {
		count += dt.countLeaves(child)
	}

	return count
}

// UpdateStatistics recalculates tree statistics
func (dt *DecisionTree) UpdateStatistics() {
	logger.Debug("Updating tree statistics")

	dt.NodeCount = dt.GetNodeCount()
	dt.LeafCount = dt.GetLeafCount()
	dt.Depth = dt.GetDepth()

	logger.Debug(fmt.Sprintf("Tree statistics updated: %d nodes, %d leaves, depth %d", dt.NodeCount, dt.LeafCount, dt.Depth))
}

// FeatureImportance represents the importance of a feature in the tree
type FeatureImportance struct {
	Feature    *Feature `json:"feature"`
	Importance float64  `json:"importance"`
	UsageCount int      `json:"usage_count"`
}

// CalculateFeatureImportance calculates feature importance based on tree structure
func (dt *DecisionTree) CalculateFeatureImportance() map[string]*FeatureImportance {
	importance := make(map[string]*FeatureImportance)

	// Initialize importance for all features in the tree
	for name, feature := range dt.Features {
		importance[name] = &FeatureImportance{
			Feature:    feature,
			Importance: 0.0,
			UsageCount: 0,
		}
	}

	// If no features exist, return empty map
	if len(dt.Features) == 0 {
		return importance
	}

	if dt.Root != nil {
		totalSamples := float64(dt.Root.Samples)
		if totalSamples > 0 {
			dt.calculateNodeImportance(dt.Root, importance, totalSamples)
		}
	}

	return importance
}

// calculateNodeImportance recursively calculates feature importance
func (dt *DecisionTree) calculateNodeImportance(node *TreeNode, importance map[string]*FeatureImportance, totalSamples float64) {
	if node == nil || node.IsLeaf() || node.Feature == nil {
		return
	}

	// Calculate weighted impurity decrease for this split
	if node.Samples > 0 {
		nodeSampleRatio := float64(node.Samples) / totalSamples

		// Calculate weighted impurity of children
		childImpurity := 0.0
		childSamples := 0

		if node.Left != nil && node.Left.Samples > 0 {
			leftRatio := float64(node.Left.Samples) / float64(node.Samples)
			childImpurity += leftRatio * node.Left.Impurity
			childSamples += node.Left.Samples
		}

		if node.Right != nil && node.Right.Samples > 0 {
			rightRatio := float64(node.Right.Samples) / float64(node.Samples)
			childImpurity += rightRatio * node.Right.Impurity
			childSamples += node.Right.Samples
		}

		for _, child := range node.Categories {
			if child != nil && child.Samples > 0 {
				childRatio := float64(child.Samples) / float64(node.Samples)
				childImpurity += childRatio * child.Impurity
				childSamples += child.Samples
			}
		}

		// Calculate importance contribution
		impurityDecrease := node.Impurity - childImpurity
		featureImportance := nodeSampleRatio * impurityDecrease

		// Update feature importance
		if featureInfo, exists := importance[node.Feature.Name]; exists {
			featureInfo.Importance += featureImportance
			featureInfo.UsageCount++
		}
	}

	// Recursively process children
	dt.calculateNodeImportance(node.Left, importance, totalSamples)
	dt.calculateNodeImportance(node.Right, importance, totalSamples)

	for _, child := range node.Categories {
		dt.calculateNodeImportance(child, importance, totalSamples)
	}
}

// GetFeatureNames returns a list of all feature names
func (dt *DecisionTree) GetFeatureNames() []string {
	names := make([]string, 0, len(dt.Features))
	for name := range dt.Features {
		names = append(names, name)
	}
	return names
}

// RemoveFeature removes a feature from the tree (only if tree is not grown)
func (dt *DecisionTree) RemoveFeature(name string) error {
	if dt.IsGrown() {
		return &ModelError{
			Op:     "remove_feature",
			Field:  "tree_state",
			Reason: "cannot remove features from a grown tree",
		}
	}

	feature, exists := dt.Features[name]
	if !exists {
		return &ModelError{
			Op:     "remove_feature",
			Field:  "name",
			Value:  name,
			Reason: "feature does not exist",
		}
	}

	// Remove from map
	delete(dt.Features, name)

	// Remove from index and reindex remaining features
	newIndex := make([]*Feature, 0, len(dt.FeaturesByIndex)-1)
	newColumnNumber := 0

	for _, f := range dt.FeaturesByIndex {
		if f != feature {
			f.ColumnNumber = newColumnNumber
			newIndex = append(newIndex, f)
			newColumnNumber++
		}
	}

	dt.FeaturesByIndex = newIndex

	return nil
}

// Clone creates a deep copy of the tree structure (without the actual tree nodes)
func (dt *DecisionTree) Clone() (*DecisionTree, error) {
	newTree := &DecisionTree{
		Features:        make(map[string]*Feature),
		FeaturesByIndex: make([]*Feature, 0, len(dt.FeaturesByIndex)),
		TargetType:      dt.TargetType,
		Config:          dt.Config, // Shallow copy of config
		NodeCount:       0,         // Reset statistics
		LeafCount:       0,
		Depth:           0,
	}

	// Clone features
	for name, feature := range dt.Features {
		newFeature, err := NewFeature(feature.Name, feature.Type, feature.ColumnNumber)
		if err != nil {
			return nil, &ModelError{
				Op:     "clone_tree",
				Field:  "feature",
				Value:  name,
				Reason: fmt.Sprintf("failed to clone feature: %v", err),
				Err:    err,
			}
		}

		// Copy feature properties
		newFeature.Values = make([]interface{}, len(feature.Values))
		copy(newFeature.Values, feature.Values)

		newFeature.CategoricalValues = make([]string, len(feature.CategoricalValues))
		copy(newFeature.CategoricalValues, feature.CategoricalValues)

		if feature.NumericRange != nil {
			newFeature.NumericRange = &Range{
				Min: feature.NumericRange.Min,
				Max: feature.NumericRange.Max,
			}
		}

		newFeature.Min = feature.Min
		newFeature.Max = feature.Max

		newTree.Features[name] = newFeature
		newTree.FeaturesByIndex = append(newTree.FeaturesByIndex, newFeature)
	}

	return newTree, nil
}
```

---
### File: pkg/models/tree_node.go
```go
// Package models contains core data structures for the decision tree implementation.
package models

import (
	"fmt"
	"math"
)

// NodeType defines the type of node in the decision tree
type NodeType string

const (
	DecisionNode NodeType = "decision" // Internal node that splits data
	LeafNode     NodeType = "leaf"     // Terminal node with prediction
)

// TreeNode represents a node in a decision tree
type TreeNode struct {
	Type              NodeType                  `json:"type"`               // Node type (decision or leaf)
	Feature           *Feature                  `json:"feature,omitempty"`  // Feature used for splitting
	Threshold         float64                   `json:"threshold"`          // Threshold for numerical splits
	Categories        map[interface{}]*TreeNode `json:"categories"`         // Children for categorical features
	Left              *TreeNode                 `json:"left"`               // Left child
	Right             *TreeNode                 `json:"right"`              // Right child
	Prediction        interface{}               `json:"prediction"`         // Prediction value for leaf nodes
	ClassDistribution map[interface{}]int       `json:"class_distribution"` // Class distribution at this node
	Samples           int                       `json:"samples"`            // Sample count at this node
	Confidence        float64                   `json:"confidence"`         // Confidence in prediction (0-1)
	Impurity          float64                   `json:"impurity"`           // Impurity measure at this node
}

// NewDecisionNode creates a decision node for numerical features with validation
func NewDecisionNode(feature *Feature, threshold float64) (*TreeNode, error) {
	if feature == nil {
		return nil, &ModelError{
			Op:     "create_decision_node",
			Field:  "feature",
			Reason: "feature cannot be nil",
		}
	}

	if feature.Type != NumericFeature && feature.Type != DateFeature {
		return nil, &ModelError{
			Op:     "create_decision_node",
			Field:  "feature_type",
			Value:  string(feature.Type),
			Reason: "decision node with threshold requires numeric or date feature",
		}
	}

	if math.IsInf(threshold, 0) || math.IsNaN(threshold) {
		return nil, &ModelError{
			Op:     "create_decision_node",
			Field:  "threshold",
			Value:  fmt.Sprintf("%.6f", threshold),
			Reason: "threshold cannot be infinite or NaN",
		}
	}

	return &TreeNode{
		Type:       DecisionNode,
		Feature:    feature,
		Threshold:  threshold,
		Categories: make(map[interface{}]*TreeNode),
		Samples:    0,
		Confidence: 0.0,
		Impurity:   0.0,
	}, nil
}

// NewCategoricalDecisionNode creates a decision node for categorical features with validation
func NewCategoricalDecisionNode(feature *Feature) (*TreeNode, error) {
	if feature == nil {
		return nil, &ModelError{
			Op:     "create_categorical_decision_node",
			Field:  "feature",
			Reason: "feature cannot be nil",
		}
	}

	if feature.Type != CategoricalFeature {
		return nil, &ModelError{
			Op:     "create_categorical_decision_node",
			Field:  "feature_type",
			Value:  string(feature.Type),
			Reason: "categorical decision node requires categorical feature",
		}
	}

	return &TreeNode{
		Type:       DecisionNode,
		Feature:    feature,
		Categories: make(map[interface{}]*TreeNode),
		Samples:    0,
		Confidence: 0.0,
		Impurity:   0.0,
	}, nil
}

// NewLeafNode creates a terminal node with prediction and validation
func NewLeafNode(prediction interface{}, distribution map[interface{}]int, samples int) (*TreeNode, error) {
	if prediction == nil {
		return nil, &ModelError{
			Op:     "create_leaf_node",
			Field:  "prediction",
			Reason: "prediction cannot be nil",
		}
	}

	if samples < 0 {
		return nil, &ModelError{
			Op:     "create_leaf_node",
			Field:  "samples",
			Value:  fmt.Sprintf("%d", samples),
			Reason: "sample count cannot be negative",
		}
	}

	if distribution == nil {
		distribution = make(map[interface{}]int)
	}

	// Validate that distribution sums to samples
	totalSamples := 0
	for _, count := range distribution {
		if count < 0 {
			return nil, &ModelError{
				Op:     "create_leaf_node",
				Field:  "class_distribution",
				Reason: "class counts cannot be negative",
			}
		}
		totalSamples += count
	}

	if samples > 0 && totalSamples != samples {
		return nil, &ModelError{
			Op:     "create_leaf_node",
			Field:  "class_distribution",
			Reason: fmt.Sprintf("distribution total (%d) does not match sample count (%d)", totalSamples, samples),
		}
	}

	// Calculate confidence and impurity
	confidence := 0.0
	impurity := 0.0

	if samples > 0 {
		// Calculate purity (confidence)
		maxCount := 0
		for _, count := range distribution {
			if count > maxCount {
				maxCount = count
			}
		}
		confidence = float64(maxCount) / float64(samples)

		// Calculate Gini impurity
		impurity = 1.0
		for _, count := range distribution {
			if count > 0 {
				prob := float64(count) / float64(samples)
				impurity -= prob * prob
			}
		}
	}

	return &TreeNode{
		Type:              LeafNode,
		Prediction:        prediction,
		ClassDistribution: distribution,
		Samples:           samples,
		Confidence:        confidence,
		Impurity:          impurity,
	}, nil
}

// IsLeaf checks if node is a leaf node
func (n *TreeNode) IsLeaf() bool {
	return n.Type == LeafNode
}

// Validate performs comprehensive validation of the tree node
func (n *TreeNode) Validate() error {
	if n.Samples < 0 {
		return &ModelError{
			Op:     "validate_node",
			Field:  "samples",
			Value:  fmt.Sprintf("%d", n.Samples),
			Reason: "sample count cannot be negative",
		}
	}

	if n.Confidence < 0.0 || n.Confidence > 1.0 {
		return &ModelError{
			Op:     "validate_node",
			Field:  "confidence",
			Value:  fmt.Sprintf("%.6f", n.Confidence),
			Reason: "confidence must be between 0.0 and 1.0",
		}
	}

	if n.Impurity < 0.0 || n.Impurity > 1.0 {
		return &ModelError{
			Op:     "validate_node",
			Field:  "impurity",
			Value:  fmt.Sprintf("%.6f", n.Impurity),
			Reason: "impurity must be between 0.0 and 1.0",
		}
	}

	if n.IsLeaf() {
		return n.validateLeafNode()
	}

	return n.validateDecisionNode()
}

// validateLeafNode validates leaf node specific constraints
func (n *TreeNode) validateLeafNode() error {
	if n.Prediction == nil {
		return &ModelError{
			Op:     "validate_leaf_node",
			Field:  "prediction",
			Reason: "leaf node must have a prediction",
		}
	}

	// Validate class distribution consistency
	totalSamples := 0
	for class, count := range n.ClassDistribution {
		if count < 0 {
			return &ModelError{
				Op:     "validate_leaf_node",
				Field:  "class_distribution",
				Value:  fmt.Sprintf("%v", class),
				Reason: "class counts cannot be negative",
			}
		}
		totalSamples += count
	}

	if n.Samples > 0 && totalSamples != n.Samples {
		return &ModelError{
			Op:     "validate_leaf_node",
			Field:  "class_distribution",
			Reason: fmt.Sprintf("distribution total (%d) does not match sample count (%d)", totalSamples, n.Samples),
		}
	}

	return nil
}

// validateDecisionNode validates decision node specific constraints
func (n *TreeNode) validateDecisionNode() error {
	if n.Feature == nil {
		return &ModelError{
			Op:     "validate_decision_node",
			Field:  "feature",
			Reason: "decision node must have a feature",
		}
	}

	// Validate feature-specific constraints
	switch n.Feature.Type {
	case NumericFeature, DateFeature:
		if math.IsInf(n.Threshold, 0) || math.IsNaN(n.Threshold) {
			return &ModelError{
				Op:     "validate_decision_node",
				Field:  "threshold",
				Value:  fmt.Sprintf("%.6f", n.Threshold),
				Reason: "threshold cannot be infinite or NaN",
			}
		}

		// For numeric features, should have left/right children
		if n.Left == nil && n.Right == nil {
			return &ModelError{
				Op:     "validate_decision_node",
				Field:  "children",
				Reason: "numeric decision node should have left and/or right children",
			}
		}

	case CategoricalFeature:
		// For categorical features, should have category children
		if len(n.Categories) == 0 {
			return &ModelError{
				Op:     "validate_decision_node",
				Field:  "categories",
				Reason: "categorical decision node should have category children",
			}
		}

	default:
		return &ModelError{
			Op:     "validate_decision_node",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "unsupported feature type for decision node",
		}
	}

	return nil
}

// GetMajorityClass returns the class with the highest count in this node
func (n *TreeNode) GetMajorityClass() interface{} {
	if len(n.ClassDistribution) == 0 {
		return nil
	}

	var majorityClass interface{}
	maxCount := -1

	for class, count := range n.ClassDistribution {
		if count > maxCount {
			maxCount = count
			majorityClass = class
		}
	}

	return majorityClass
}

// GetPurity returns the purity of this node (ratio of majority class)
func (n *TreeNode) GetPurity() float64 {
	if n.Samples == 0 {
		return 0.0
	}

	majorityClass := n.GetMajorityClass()
	if majorityClass == nil {
		return 0.0
	}

	return float64(n.ClassDistribution[majorityClass]) / float64(n.Samples)
}

// GetImpurity returns the Gini impurity of this node
func (n *TreeNode) GetImpurity() float64 {
	if n.Samples == 0 {
		return 0.0
	}

	impurity := 1.0
	for _, count := range n.ClassDistribution {
		if count > 0 {
			prob := float64(count) / float64(n.Samples)
			impurity -= prob * prob
		}
	}

	return impurity
}

// UpdateStatistics recalculates confidence and impurity based on current distribution
func (n *TreeNode) UpdateStatistics() {
	if n.Samples == 0 {
		n.Confidence = 0.0
		n.Impurity = 0.0
		return
	}

	// Calculate confidence (purity)
	maxCount := 0
	for _, count := range n.ClassDistribution {
		if count > maxCount {
			maxCount = count
		}
	}
	n.Confidence = float64(maxCount) / float64(n.Samples)

	// Calculate Gini impurity
	n.Impurity = n.GetImpurity()
}

// AddSample adds a sample to this node's statistics
func (n *TreeNode) AddSample(class interface{}) error {
	if class == nil {
		return &ModelError{
			Op:     "add_sample",
			Field:  "class",
			Reason: "class cannot be nil",
		}
	}

	if n.ClassDistribution == nil {
		n.ClassDistribution = make(map[interface{}]int)
	}

	n.ClassDistribution[class]++
	n.Samples++
	n.UpdateStatistics()

	return nil
}

// GetChildCount returns the number of child nodes
func (n *TreeNode) GetChildCount() int {
	if n.IsLeaf() {
		return 0
	}

	count := 0
	if n.Left != nil {
		count++
	}
	if n.Right != nil {
		count++
	}
	count += len(n.Categories)

	return count
}

// GetChildren returns all child nodes
func (n *TreeNode) GetChildren() []*TreeNode {
	if n.IsLeaf() {
		return nil
	}

	var children []*TreeNode

	if n.Left != nil {
		children = append(children, n.Left)
	}
	if n.Right != nil {
		children = append(children, n.Right)
	}

	for _, child := range n.Categories {
		if child != nil {
			children = append(children, child)
		}
	}

	return children
}

// SetChild sets a child node for categorical features
func (n *TreeNode) SetChild(category interface{}, child *TreeNode) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_child",
			Reason: "cannot set children on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_child",
			Field:  "feature",
			Reason: "decision node must have a feature to set children",
		}
	}

	if n.Feature.Type != CategoricalFeature {
		return &ModelError{
			Op:     "set_child",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set categorical children on categorical features",
		}
	}

	if category == nil {
		return &ModelError{
			Op:     "set_child",
			Field:  "category",
			Reason: "category cannot be nil",
		}
	}

	if n.Categories == nil {
		n.Categories = make(map[interface{}]*TreeNode)
	}

	n.Categories[category] = child
	return nil
}

// SetLeftChild sets the left child for numeric features
func (n *TreeNode) SetLeftChild(child *TreeNode) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_left_child",
			Reason: "cannot set children on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_left_child",
			Field:  "feature",
			Reason: "decision node must have a feature to set children",
		}
	}

	if n.Feature.Type != NumericFeature && n.Feature.Type != DateFeature {
		return &ModelError{
			Op:     "set_left_child",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set left child on numeric or date features",
		}
	}

	n.Left = child
	return nil
}

// SetRightChild sets the right child for numeric features
func (n *TreeNode) SetRightChild(child *TreeNode) error {
	if n.IsLeaf() {
		return &ModelError{
			Op:     "set_right_child",
			Reason: "cannot set children on leaf node",
		}
	}

	if n.Feature == nil {
		return &ModelError{
			Op:     "set_right_child",
			Field:  "feature",
			Reason: "decision node must have a feature to set children",
		}
	}

	if n.Feature.Type != NumericFeature && n.Feature.Type != DateFeature {
		return &ModelError{
			Op:     "set_right_child",
			Field:  "feature_type",
			Value:  string(n.Feature.Type),
			Reason: "can only set right child on numeric or date features",
		}
	}

	n.Right = child
	return nil
}
```
