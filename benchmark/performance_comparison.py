#!/usr/bin/env python3
"""
Performance-focused comparison between Go and Python implementations.
"""
import json
import time
import psutil
import subprocess
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple

def measure_memory_usage(process):
    """Measure peak memory usage of a process."""
    peak_memory = 0
    try:
        while process.poll() is None:
            try:
                memory_info = psutil.Process(process.pid).memory_info()
                current_memory = memory_info.rss / 1024 / 1024  # MB
                peak_memory = max(peak_memory, current_memory)
                time.sleep(0.1)
            except psutil.NoSuchProcess:
                break
    except Exception:
        pass
    return peak_memory

def benchmark_implementation(impl_name: str, train_cmd: List[str], predict_cmd: List[str]) -> Dict:
    """Benchmark a single implementation."""
    results = {
        'implementation': impl_name,
        'training_time': 0,
        'prediction_time': 0,
        'training_memory_mb': 0,
        'prediction_memory_mb': 0,
        'success': True,
        'error': None
    }
    
    try:
        # Training benchmark
        start_time = time.time()
        train_process = subprocess.Popen(train_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        training_memory = measure_memory_usage(train_process)
        train_process.wait()
        training_time = time.time() - start_time
        
        if train_process.returncode != 0:
            results['success'] = False
            results['error'] = f"Training failed: {train_process.stderr.read().decode()}"
            return results
        
        # Prediction benchmark
        start_time = time.time()
        predict_process = subprocess.Popen(predict_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        prediction_memory = measure_memory_usage(predict_process)
        predict_process.wait()
        prediction_time = time.time() - start_time
        
        if predict_process.returncode != 0:
            results['success'] = False
            results['error'] = f"Prediction failed: {predict_process.stderr.read().decode()}"
            return results
        
        results.update({
            'training_time': training_time,
            'prediction_time': prediction_time,
            'training_memory_mb': training_memory,
            'prediction_memory_mb': prediction_memory
        })
        
    except Exception as e:
        results['success'] = False
        results['error'] = str(e)
    
    return results

def run_performance_comparison(dataset_name: str, data_dir: Path) -> Dict:
    """Run performance comparison for a specific dataset."""
    train_file = data_dir / f"{dataset_name}_train.csv"
    predict_file = data_dir / f"{dataset_name}_predict.csv"
    
    if not train_file.exists() or not predict_file.exists():
        return {'error': f"Dataset files not found for {dataset_name}"}
    
    # Get target column from config
    with open('benchmark.yaml', 'r') as f:
        config = yaml.safe_load(f)
    target_col = config['datasets'][dataset_name]['target_column']
    
    results = []
    
    # Go implementation
    go_train_cmd = [
        './mulberri', '-c', 'train',
        '-i', str(train_file),
        '-t', target_col,
        '-o', f'go_{dataset_name}_model.dt'
    ]
    go_predict_cmd = [
        './mulberri', '-c', 'predict',
        '-i', str(predict_file),
        '-m', f'go_{dataset_name}_model.dt',
        '-o', f'go_{dataset_name}_predictions.csv'
    ]
    
    go_results = benchmark_implementation('Go', go_train_cmd, go_predict_cmd)
    results.append(go_results)
    
    # Python implementation
    python_train_cmd = [
        'python', 'run_benchmark.py',
        '--implementations', 'python',
        '--datasets', dataset_name,
        '--quiet'
    ]
    
    # For Python, we'll use the existing benchmark framework
    # This is a simplified version - you might want to create a direct Python benchmark
    
    return {
        'dataset': dataset_name,
        'results': results
    }

def main():
    """Main performance comparison."""
    datasets = ['bank', 'credit_card', 'home_loan']
    data_dir = Path('data')
    
    all_results = []
    
    for dataset in datasets:
        print(f"Benchmarking {dataset}...")
        result = run_performance_comparison(dataset, data_dir)
        all_results.append(result)
    
    # Save results
    with open('performance_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    # Print summary
    print("\nPerformance Comparison Summary:")
    print("=" * 50)
    
    for result in all_results:
        if 'error' in result:
            print(f"{result.get('dataset', 'Unknown')}: {result['error']}")
            continue
            
        dataset = result['dataset']
        print(f"\nDataset: {dataset}")
        
        for impl_result in result['results']:
            if impl_result['success']:
                print(f"  {impl_result['implementation']}:")
                print(f"    Training: {impl_result['training_time']:.2f}s, {impl_result['training_memory_mb']:.1f}MB")
                print(f"    Prediction: {impl_result['prediction_time']:.2f}s, {impl_result['prediction_memory_mb']:.1f}MB")
            else:
                print(f"  {impl_result['implementation']}: FAILED - {impl_result['error']}")

if __name__ == "__main__":
    main()
