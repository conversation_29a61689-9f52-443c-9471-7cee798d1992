#!/usr/bin/env python3
"""
Generate performance comparison charts for Go vs Python benchmarks.
"""
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

def create_performance_charts():
    """Create performance comparison charts."""
    
    # Data from benchmark results
    datasets = ['Bank Marketing', 'Credit Card', 'Home Loan', 'Student Success']
    
    # Training times (seconds)
    go_training = [0.21, 3.08, 0.03, 0.35]
    python_training = [4.58, 25.77, 2.16, 11.52]
    
    # Prediction times (seconds)
    go_prediction = [0.03, 0.15, 0.01, 0.03]
    python_prediction = [0.93, 0.99, 0.81, 0.81]
    
    # Accuracy (%)
    go_accuracy = [88.95, 81.65, 78.86, 78.76]
    python_accuracy = [88.40, 81.55, 69.11, 78.53]
    
    # F1-Scores
    go_f1 = [0.4681, 0.4345, 0.8539, 0.8000]
    python_f1 = [0.3713, 0.4279, 0.7841, 0.7966]
    
    # Create subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Go vs Python Decision Tree Performance Comparison', fontsize=16, fontweight='bold')
    
    # Training Time Comparison
    x = np.arange(len(datasets))
    width = 0.35
    
    ax1.bar(x - width/2, go_training, width, label='Go (Mulberri)', color='#2E8B57', alpha=0.8)
    ax1.bar(x + width/2, python_training, width, label='Python C4.5', color='#FF6B6B', alpha=0.8)
    ax1.set_ylabel('Training Time (seconds)')
    ax1.set_title('Training Time Comparison')
    ax1.set_xticks(x)
    ax1.set_xticklabels(datasets, rotation=45, ha='right')
    ax1.legend()
    ax1.set_yscale('log')  # Log scale due to large differences
    
    # Add speedup annotations
    speedups = [python_training[i]/go_training[i] for i in range(len(datasets))]
    for i, speedup in enumerate(speedups):
        ax1.annotate(f'{speedup:.1f}x faster', 
                    xy=(i, max(go_training[i], python_training[i])), 
                    xytext=(0, 10), textcoords='offset points',
                    ha='center', fontsize=9, fontweight='bold', color='green')
    
    # Prediction Time Comparison
    ax2.bar(x - width/2, go_prediction, width, label='Go (Mulberri)', color='#2E8B57', alpha=0.8)
    ax2.bar(x + width/2, python_prediction, width, label='Python C4.5', color='#FF6B6B', alpha=0.8)
    ax2.set_ylabel('Prediction Time (seconds)')
    ax2.set_title('Prediction Time Comparison')
    ax2.set_xticks(x)
    ax2.set_xticklabels(datasets, rotation=45, ha='right')
    ax2.legend()
    
    # Add speedup annotations
    pred_speedups = [python_prediction[i]/go_prediction[i] for i in range(len(datasets))]
    for i, speedup in enumerate(pred_speedups):
        ax2.annotate(f'{speedup:.1f}x faster', 
                    xy=(i, max(go_prediction[i], python_prediction[i])), 
                    xytext=(0, 10), textcoords='offset points',
                    ha='center', fontsize=9, fontweight='bold', color='green')
    
    # Accuracy Comparison
    ax3.bar(x - width/2, go_accuracy, width, label='Go (Mulberri)', color='#2E8B57', alpha=0.8)
    ax3.bar(x + width/2, python_accuracy, width, label='Python C4.5', color='#FF6B6B', alpha=0.8)
    ax3.set_ylabel('Accuracy (%)')
    ax3.set_title('Accuracy Comparison')
    ax3.set_xticks(x)
    ax3.set_xticklabels(datasets, rotation=45, ha='right')
    ax3.legend()
    ax3.set_ylim(60, 95)  # Focus on the relevant range
    
    # Add accuracy difference annotations
    acc_diffs = [go_accuracy[i] - python_accuracy[i] for i in range(len(datasets))]
    for i, diff in enumerate(acc_diffs):
        color = 'green' if diff > 0 else 'red'
        ax3.annotate(f'{diff:+.1f}%', 
                    xy=(i, max(go_accuracy[i], python_accuracy[i])), 
                    xytext=(0, 5), textcoords='offset points',
                    ha='center', fontsize=9, fontweight='bold', color=color)
    
    # F1-Score Comparison
    ax4.bar(x - width/2, go_f1, width, label='Go (Mulberri)', color='#2E8B57', alpha=0.8)
    ax4.bar(x + width/2, python_f1, width, label='Python C4.5', color='#FF6B6B', alpha=0.8)
    ax4.set_ylabel('F1-Score')
    ax4.set_title('F1-Score Comparison')
    ax4.set_xticks(x)
    ax4.set_xticklabels(datasets, rotation=45, ha='right')
    ax4.legend()
    ax4.set_ylim(0, 1)
    
    # Add F1 difference annotations
    f1_diffs = [go_f1[i] - python_f1[i] for i in range(len(datasets))]
    for i, diff in enumerate(f1_diffs):
        color = 'green' if diff > 0 else 'red'
        ax4.annotate(f'{diff:+.3f}', 
                    xy=(i, max(go_f1[i], python_f1[i])), 
                    xytext=(0, 5), textcoords='offset points',
                    ha='center', fontsize=9, fontweight='bold', color=color)
    
    plt.tight_layout()
    plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('performance_comparison.pdf', bbox_inches='tight')
    print("Charts saved as performance_comparison.png and performance_comparison.pdf")
    
    # Create summary statistics chart
    create_summary_chart()

def create_summary_chart():
    """Create a summary chart showing overall performance ratios."""
    
    metrics = ['Training Speed', 'Prediction Speed', 'Accuracy', 'F1-Score']
    ratios = [33.8, 36.4, 1.038, 1.092]  # Go vs Python ratios
    colors = ['#2E8B57', '#2E8B57', '#4169E1', '#4169E1']
    
    fig, ax = plt.subplots(figsize=(10, 6))
    bars = ax.bar(metrics, ratios, color=colors, alpha=0.8)
    
    ax.set_ylabel('Performance Ratio (Go vs Python)')
    ax.set_title('Overall Performance Summary: Go vs Python', fontsize=14, fontweight='bold')
    ax.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Equal Performance')
    
    # Add value labels on bars
    for bar, ratio in zip(bars, ratios):
        height = bar.get_height()
        ax.annotate(f'{ratio:.1f}x' if ratio > 2 else f'{ratio:.3f}x',
                   xy=(bar.get_x() + bar.get_width() / 2, height),
                   xytext=(0, 3), textcoords="offset points",
                   ha='center', va='bottom', fontweight='bold')
    
    ax.legend()
    ax.set_yscale('log')
    plt.tight_layout()
    plt.savefig('summary_performance.png', dpi=300, bbox_inches='tight')
    plt.savefig('summary_performance.pdf', bbox_inches='tight')
    print("Summary chart saved as summary_performance.png and summary_performance.pdf")

if __name__ == "__main__":
    try:
        import matplotlib.pyplot as plt
        create_performance_charts()
        print("Performance charts generated successfully!")
    except ImportError:
        print("matplotlib not installed. Install with: pip install matplotlib")
        print("Charts not generated, but benchmark data is available in markdown files.")
