#!/usr/bin/env python3
"""Quick performance test with synthetic data."""
import time
import subprocess
import pandas as pd
import numpy as np
from pathlib import Path

def create_test_data(n_rows=1000, n_features=5):
    """Create synthetic test data."""
    np.random.seed(42)
    
    data = {}
    for i in range(n_features):
        if i < n_features // 2:
            # Numeric features
            data[f'numeric_{i}'] = np.random.normal(50, 15, n_rows)
        else:
            # Categorical features
            categories = ['A', 'B', 'C', 'D']
            data[f'categorical_{i}'] = np.random.choice(categories, n_rows)
    
    # Target variable
    data['target'] = np.random.choice(['yes', 'no'], n_rows)
    
    df = pd.DataFrame(data)
    return df

def benchmark_go(train_file, test_file, model_file, output_file):
    """Benchmark Go implementation."""
    start_time = time.time()
    
    # Training
    train_cmd = ['./mulberri', '-c', 'train', '-i', train_file, '-t', 'target', '-o', model_file]
    result = subprocess.run(train_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        return None, f"Training failed: {result.stderr}"
    
    train_time = time.time() - start_time
    
    # Prediction
    start_time = time.time()
    predict_cmd = ['./mulberri', '-c', 'predict', '-i', test_file, '-m', model_file, '-o', output_file]
    result = subprocess.run(predict_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        return None, f"Prediction failed: {result.stderr}"
    
    predict_time = time.time() - start_time
    
    return {'train_time': train_time, 'predict_time': predict_time}, None

def main():
    """Run quick performance test."""
    sizes = [100, 500, 1000, 5000]
    
    print("Quick Performance Test")
    print("=" * 40)
    
    for size in sizes:
        print(f"\nTesting with {size} rows...")
        
        # Create test data
        train_df = create_test_data(size)
        test_df = create_test_data(size // 4)  # Smaller test set
        
        train_file = f'test_train_{size}.csv'
        test_file = f'test_predict_{size}.csv'
        model_file = f'test_model_{size}.dt'
        output_file = f'test_output_{size}.csv'
        
        train_df.to_csv(train_file, index=False)
        test_df.to_csv(test_file, index=False)
        
        # Benchmark Go
        go_result, error = benchmark_go(train_file, test_file, model_file, output_file)
        
        if go_result:
            print(f"  Go: Train={go_result['train_time']:.3f}s, Predict={go_result['predict_time']:.3f}s")
        else:
            print(f"  Go: FAILED - {error}")
        
        # Cleanup
        for f in [train_file, test_file, model_file, output_file]:
            Path(f).unlink(missing_ok=True)

if __name__ == "__main__":
    main()
