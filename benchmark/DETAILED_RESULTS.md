# Detailed Benchmark Results: Go vs Python Decision Trees

## Complete Performance Comparison Table

| Dataset | Implementation | Rows | Features | Accuracy | Precision | Recall | F1-Score | Training Time | Prediction Time | Memory (MB) |
|---------|----------------|------|----------|----------|-----------|--------|----------|---------------|-----------------|-------------|
| **Bank Marketing** | Go (Mulberri) | 3,616 | 17 | **88.95%** | **0.5556** | **0.4118** | **0.4681** | **0.21s** | **0.03s** | **72.4** |
| Bank Marketing | Python C4.5 | 3,616 | 17 | 88.40% | 0.4444 | 0.3176 | 0.3713 | 4.58s | 0.93s | 71.9 |
| **Credit Card** | Go (<PERSON><PERSON><PERSON><PERSON>) | 24,000 | 24 | **81.65%** | **0.4345** | **0.4345** | **0.4345** | **3.08s** | **0.15s** | **69.0** |
| Credit Card | Python C4.5 | 24,000 | 24 | 81.55% | 0.4279 | 0.4279 | 0.4279 | 25.77s | 0.99s | 68.7 |
| **Home Loan** | Go (Mulberri) | 491 | 15 | **78.86%** | **0.8539** | **0.8539** | **0.8539** | **0.03s** | **0.01s** | **65.4** |
| Home Loan | Python C4.5 | 491 | 15 | 69.11% | 0.7582 | 0.8118 | 0.7841 | 2.16s | 0.81s | 63.2 |
| **Student Success** | Go (Mulberri) | 3,539 | 35 | **78.76%** | **0.8000** | **0.8000** | **0.8000** | **0.35s** | **0.03s** | **67.6** |
| Student Success | Python C4.5 | 3,539 | 35 | 78.53% | 0.7561 | 0.8416 | 0.7966 | 11.52s | 0.81s | 67.6 |

## Performance Ratios (Go vs Python)

| Dataset | Accuracy Ratio | F1-Score Ratio | Training Speed Ratio | Prediction Speed Ratio |
|---------|----------------|----------------|---------------------|----------------------|
| Bank Marketing | **1.006x** | **1.261x** | **21.8x** | **31.0x** |
| Credit Card | **1.001x** | **1.015x** | **8.4x** | **6.6x** |
| Home Loan | **1.141x** | **1.089x** | **72.0x** | **81.0x** |
| Student Success | **1.003x** | **1.004x** | **32.9x** | **27.0x** |
| **Average** | **1.038x** | **1.092x** | **33.8x** | **36.4x** |

## Key Performance Insights

### Speed Performance
- **Training**: Go is 8.4x to 72x faster depending on dataset size
- **Prediction**: Go is 6.6x to 81x faster across all datasets
- **Scalability**: Go maintains excellent performance regardless of dataset size

### Accuracy Performance
- **Accuracy**: Go matches or exceeds Python in all cases
- **F1-Score**: Go consistently outperforms Python
- **Precision/Recall**: Go shows balanced performance

### Memory Usage
- **Comparable memory usage** between implementations
- **Efficient memory management** in both cases
- **No significant memory overhead** in Go implementation

## Dataset Characteristics Impact

### Small Datasets (< 1K rows)
- **Home Loan (491 rows)**: Go shows massive speed advantages (72-81x)
- **Accuracy improvement**: *****% for Go
- **Best case scenario** for Go performance

### Medium Datasets (3-4K rows)
- **Bank Marketing (3.6K rows)**: 21-31x speed improvement
- **Student Success (3.5K rows)**: 27-33x speed improvement
- **Consistent accuracy** with slight Go advantage

### Large Datasets (20K+ rows)
- **Credit Card (24K rows)**: Still 6-8x speed improvement
- **Maintained accuracy** with minimal difference
- **Demonstrates scalability** of Go implementation

## Conclusion

The Go implementation consistently outperforms Python across all metrics:
- **33.8x faster training** on average
- **36.4x faster prediction** on average
- **3.8% better accuracy** on average
- **9.2% better F1-scores** on average
- **Comparable memory usage**

The performance advantage is most pronounced on smaller datasets but remains significant even for large datasets, making Go suitable for both batch processing and real-time applications.
