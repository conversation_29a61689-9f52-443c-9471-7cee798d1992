graph TB
    %% External Dependencies
    subgraph "External"
        FS[File System]
        StdLib[Go Standard Library<br/>- context<br/>- encoding/csv<br/>- encoding/json<br/>- os, syscall]
    end
    
    %% CLI Layer
    subgraph "CLI Layer"
        CLIFlags[CLI Flags Parser<br/>cmd/mulberri/cli/flags.go]
        CLIConfig[CLI Config<br/>cmd/mulberri/cli/config.go]
        CLIValidation[CLI Validation<br/>Input validation & help]
    end
    
    %% Main Orchestration
    subgraph "Main Orchestration"
        MainEntry[Main Entry Point<br/>cmd/mulberri/main.go]
        TrainHandler[Train Command Handler<br/>handleTrainCommand]
        PredictHandler[Predict Command Handler<br/>handlePredictCommand]
        ProgressReporter[Progress Reporter<br/>User feedback]
        Serialization[Model Serialization<br/>JSON marshal/unmarshal]
    end
    
    %% Core Models
    subgraph "Core Models (pkg/models)"
        DecisionTreeModel[DecisionTree<br/>- Root TreeNode<br/>- Features<br/>- Statistics]
        TreeNodeModel[TreeNode<br/>- Decision/Leaf nodes<br/>- Split criteria<br/>- Class distributions]
        FeatureStatsModel[FeatureStats<br/>- Name, Type<br/>- Ranges, Values<br/>- Column mapping]
    end
    
    %% Training Components
    subgraph "Training Components"
        FeatureCreator[Feature Creator<br/>internals/training/feature_creation.go]
        C45Splitter[C45 Splitter<br/>internals/training/split.go]
        TreeBuilder[Tree Builder<br/>internals/training/builder/]
        ImpurityCalc[Impurity Calculator<br/>internals/training/impurity.go]
        SplitValidator[Split Validator<br/>Stopping criteria]
    end
    
    %% Prediction Components
    subgraph "Prediction Components"
        TreeTraverser[Tree Traverser<br/>internals/prediction/tree_traversal.go]
        DataValidator[Data Validator<br/>internals/prediction/data_validator.go]
        TraversalEngine[Traversal Engine<br/>Multiple strategies]
        ConfidenceCalc[Confidence Calculator<br/>Probability computation]
    end
    
    %% Utility Components
    subgraph "Utility Components"
        CSVReader[CSV Reader<br/>internals/utils/csv_loader.go]
        FeatureParser[Feature Info Parser<br/>internals/utils/feature_info.go]
        DatasetImpl[CSV Dataset<br/>internals/utils/dataset.go]
        PredictionLoader[Prediction Loader<br/>internals/utils/prediction_loader.go]
        ResultWriter[Result Writer<br/>internals/utils/prediction_writer.go]
    end
    
    %% Shared Services
    subgraph "Shared Services"
        Logger[Logger<br/>pkg/logger/logger.go]
        Validator[Validator<br/>pkg/validation/]
        ErrorHandler[Error Handler<br/>Structured errors]
    end
    
    %% External Connections
    FS --> CSVReader
    FS --> FeatureParser
    FS --> PredictionLoader
    FS --> Serialization
    ResultWriter --> FS
    StdLib --> MainEntry
    StdLib --> CSVReader
    StdLib --> Serialization
    
    %% CLI Flow
    CLIFlags --> CLIConfig
    CLIConfig --> CLIValidation
    CLIValidation --> MainEntry
    
    %% Main Orchestration Flow
    MainEntry --> TrainHandler
    MainEntry --> PredictHandler
    TrainHandler --> ProgressReporter
    PredictHandler --> ProgressReporter
    
    %% Training Flow
    TrainHandler --> FeatureParser
    TrainHandler --> CSVReader
    TrainHandler --> FeatureCreator
    TrainHandler --> DatasetImpl
    TrainHandler --> C45Splitter
    TrainHandler --> TreeBuilder
    TrainHandler --> Serialization
    
    %% Prediction Flow
    PredictHandler --> Serialization
    PredictHandler --> PredictionLoader
    PredictHandler --> DataValidator
    PredictHandler --> TreeTraverser
    PredictHandler --> ResultWriter
    
    %% Training Component Interactions
    FeatureCreator --> FeatureStatsModel
    DatasetImpl --> FeatureStatsModel
    C45Splitter --> ImpurityCalc
    C45Splitter --> DatasetImpl
    TreeBuilder --> C45Splitter
    TreeBuilder --> SplitValidator
    TreeBuilder --> TreeNodeModel
    TreeBuilder --> DecisionTreeModel
    
    %% Prediction Component Interactions
    DataValidator --> FeatureStatsModel
    TreeTraverser --> TraversalEngine
    TreeTraverser --> ConfidenceCalc
    TreeTraverser --> DecisionTreeModel
    TreeTraverser --> TreeNodeModel
    
    %% Shared Service Usage
    MainEntry --> Logger
    TrainHandler --> Logger
    PredictHandler --> Logger
    FeatureCreator --> Logger
    TreeBuilder --> Logger
    TreeTraverser --> Logger
    
    CSVReader --> Validator
    FeatureParser --> Validator
    DataValidator --> Validator
    
    TrainHandler --> ErrorHandler
    PredictHandler --> ErrorHandler
    TreeBuilder --> ErrorHandler
    TreeTraverser --> ErrorHandler
    
    %% Model Dependencies
    TreeBuilder --> DecisionTreeModel
    TreeBuilder --> TreeNodeModel
    TreeBuilder --> FeatureStatsModel
    TreeTraverser --> DecisionTreeModel
    TreeTraverser --> TreeNodeModel
    Serialization --> DecisionTreeModel
    
    %% Styling
    classDef external fill:#f5f5f5,stroke:#666
    classDef cli fill:#e1f5fe,stroke:#0277bd
    classDef main fill:#f3e5f5,stroke:#7b1fa2
    classDef core fill:#e8f5e8,stroke:#388e3c
    classDef training fill:#fff3e0,stroke:#f57c00
    classDef prediction fill:#fce4ec,stroke:#c2185b
    classDef utility fill:#f1f8e9,stroke:#689f38
    classDef shared fill:#fff8e1,stroke:#ffa000
    
    class FS,StdLib external
    class CLIFlags,CLIConfig,CLIValidation cli
    class MainEntry,TrainHandler,PredictHandler,ProgressReporter,Serialization main
    class DecisionTreeModel,TreeNodeModel,FeatureStatsModel core
    class FeatureCreator,C45Splitter,TreeBuilder,ImpurityCalc,SplitValidator training
    class TreeTraverser,DataValidator,TraversalEngine,ConfidenceCalc prediction
    class CSVReader,FeatureParser,DatasetImpl,PredictionLoader,ResultWriter utility
    class Logger,Validator,ErrorHandler shared
