# Mulberri System Architecture Diagrams

This folder contains Mermaid diagrams that visualize the Mulberri decision tree system architecture and code flow.

## 📁 Files

| File | Description |
|------|-------------|
| `system-architecture.mmd` | High-level system architecture overview |
| `data-flow-pipeline.mmd` | Data flow and processing pipeline |
| `component-interactions.mmd` | Component interactions and dependencies |
| `usage-flow-sequence.mmd` | Usage flow sequence diagram |
| `view-diagrams.html` | Local HTML viewer for all diagrams |
| `README.md` | This file |

## 🚀 Quick Start

### Option 1: Online Viewing (Recommended)
1. Go to [Mermaid Live Editor](https://mermaid.live/)
2. Copy content from any `.mmd` file
3. Paste into the editor
4. View the rendered diagram

### Option 2: Local HTML Viewer
1. Open `view-diagrams.html` in your web browser
2. Navigate between different diagrams using the tabs
3. Follow the instructions to view diagrams in Mermaid Live Editor

### Option 3: VS Code
1. Install the "Mermaid Preview" extension
2. Open any `.mmd` file
3. Use `Ctrl+Shift+P` → "Mermaid: Preview"

## 📊 Diagram Overview

### 1. System Architecture (`system-architecture.mmd`)
- **Purpose**: Shows the overall system structure
- **Key Components**: CLI layer, main orchestrator, core packages, training/prediction pipelines
- **Best For**: Understanding high-level architecture

### 2. Data Flow Pipeline (`data-flow-pipeline.mmd`)
- **Purpose**: Illustrates how data flows through the system
- **Key Elements**: Input data, processing stages, data transformations, output formats
- **Best For**: Understanding data processing workflow

### 3. Component Interactions (`component-interactions.mmd`)
- **Purpose**: Details component dependencies and interactions
- **Key Elements**: Package dependencies, shared services, external interfaces
- **Best For**: Understanding internal architecture and dependencies

### 4. Usage Flow Sequence (`usage-flow-sequence.mmd`)
- **Purpose**: Shows step-by-step user interactions
- **Key Elements**: Training workflow, prediction workflow, error handling
- **Best For**: Understanding user workflows and system behavior

## 🔧 Export Options

From Mermaid Live Editor, you can export diagrams as:
- PNG (for presentations, documents)
- SVG (for scalable graphics)
- PDF (for documentation)

## 📚 Related Documentation

- `../SYSTEM_ARCHITECTURE.md` - Comprehensive system documentation
- `../DIAGRAM_SETUP_GUIDE.md` - Detailed setup instructions

## 💡 Tips

1. **Start with System Architecture** - Get the big picture first
2. **Use Mermaid Live Editor** - Best rendering and export options
3. **Zoom and Pan** - Most viewers support zooming for detailed inspection
4. **Export for Sharing** - PNG/SVG exports work in all documentation tools

## 🆘 Need Help?

- Check `../DIAGRAM_SETUP_GUIDE.md` for detailed setup instructions
- Visit [Mermaid Documentation](https://mermaid-js.github.io/mermaid/) for syntax help
- Use [Mermaid Live Editor](https://mermaid.live/) for immediate viewing
