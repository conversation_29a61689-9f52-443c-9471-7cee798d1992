graph TB
    %% CLI Layer
    CLI[CLI Interface<br/>cmd/mulberri/cli] --> Main[Main Orchestrator<br/>cmd/mulberri/main.go]
    
    %% Main Commands
    Main --> TrainCmd[Train Command<br/>handleTrainCommand]
    Main --> PredictCmd[Predict Command<br/>handlePredictCommand]
    Main --> VersionCmd[Version Command]
    
    %% Training Flow
    TrainCmd --> LoadFeature[Load Feature Info<br/>internals/utils]
    LoadFeature --> ProcessCSV[Process CSV Data<br/>internals/utils]
    ProcessCSV --> BuildTree[Build Decision Tree<br/>internals/training/builder]
    BuildTree --> SaveModel[Save Model<br/>serialization.go]
    
    %% Prediction Flow
    PredictCmd --> LoadModel[Load Model<br/>serialization.go]
    LoadModel --> LoadPredData[Load Prediction Data<br/>internals/utils]
    LoadPredData --> MakePred[Make Predictions<br/>internals/prediction]
    MakePred --> SaveResults[Save Results<br/>internals/utils]
    
    %% Core Components
    subgraph "Core Packages"
        Models[pkg/models<br/>- DecisionTree<br/>- TreeNode<br/>- FeatureStats]
        Logger[pkg/logger<br/>Structured Logging]
        Validation[pkg/validation<br/>Input Validation]
    end
    
    %% Training Components
    subgraph "Training Pipeline"
        FeatureCreation[Feature Creation<br/>internals/training]
        Splitter[C45 Splitter<br/>internals/training]
        TreeBuilder[Tree Builder<br/>internals/training/builder]
        Dataset[CSV Dataset<br/>internals/utils]
    end
    
    %% Prediction Components
    subgraph "Prediction Pipeline"
        TreeTraversal[Tree Traversal<br/>internals/prediction]
        DataValidator[Data Validator<br/>internals/prediction]
        ResultWriter[Result Writer<br/>internals/utils]
    end
    
    %% Utilities
    subgraph "Utilities"
        CSVLoader[CSV Loader<br/>internals/utils]
        FeatureParser[Feature Parser<br/>internals/utils]
        PredictionLoader[Prediction Loader<br/>internals/utils]
        PredictionWriter[Prediction Writer<br/>internals/utils]
    end
    
    %% Data Flow Connections
    ProcessCSV --> FeatureCreation
    FeatureCreation --> Dataset
    Dataset --> Splitter
    Splitter --> TreeBuilder
    TreeBuilder --> Models
    
    LoadPredData --> DataValidator
    DataValidator --> TreeTraversal
    TreeTraversal --> ResultWriter
    
    %% Utility Connections
    LoadFeature --> FeatureParser
    ProcessCSV --> CSVLoader
    LoadPredData --> PredictionLoader
    SaveResults --> PredictionWriter
    
    %% Core Package Usage
    FeatureCreation --> Models
    TreeBuilder --> Models
    TreeTraversal --> Models
    Main --> Logger
    
    %% External Interfaces
    FileSystem[(File System<br/>CSV Files, YAML, JSON)]
    FileSystem --> CSVLoader
    FileSystem --> FeatureParser
    FileSystem --> PredictionLoader
    SaveModel --> FileSystem
    SaveResults --> FileSystem
    
    %% Styling
    classDef cliLayer fill:#e1f5fe
    classDef mainLayer fill:#f3e5f5
    classDef coreLayer fill:#e8f5e8
    classDef trainingLayer fill:#fff3e0
    classDef predictionLayer fill:#fce4ec
    classDef utilLayer fill:#f1f8e9
    classDef external fill:#f5f5f5
    
    class CLI,Main cliLayer
    class TrainCmd,PredictCmd,VersionCmd mainLayer
    class Models,Logger,Validation coreLayer
    class FeatureCreation,Splitter,TreeBuilder,Dataset trainingLayer
    class TreeTraversal,DataValidator,ResultWriter predictionLayer
    class CSVLoader,FeatureParser,PredictionLoader,PredictionWriter utilLayer
    class FileSystem external
