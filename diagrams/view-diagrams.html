<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mulberri System Architecture Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .nav {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        .nav button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .nav button:hover {
            background: #0056b3;
        }
        .nav button.active {
            background: #28a745;
        }
        .diagram-section {
            display: none;
            padding: 30px;
        }
        .diagram-section.active {
            display: block;
        }
        .diagram-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.8em;
            font-weight: 500;
        }
        .diagram-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
            font-size: 1.1em;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
            font-style: italic;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .instructions {
            background: #d1ecf1;
            color: #0c5460;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            line-height: 1.6;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mulberri System Architecture</h1>
            <p>Interactive Diagrams and Code Flow Documentation</p>
        </div>
        
        <div class="nav">
            <button onclick="showDiagram('overview')" class="active" id="btn-overview">System Overview</button>
            <button onclick="showDiagram('dataflow')" id="btn-dataflow">Data Flow</button>
            <button onclick="showDiagram('components')" id="btn-components">Component Interactions</button>
            <button onclick="showDiagram('sequence')" id="btn-sequence">Usage Flow</button>
        </div>

        <div id="overview" class="diagram-section active">
            <h2 class="diagram-title">System Architecture Overview</h2>
            <p class="diagram-description">
                High-level view of the Mulberri decision tree system showing the main components, 
                their relationships, and the flow between CLI interface, core packages, training pipeline, 
                and prediction pipeline.
            </p>
            <div class="instructions">
                <strong>Instructions:</strong> To view this diagram, copy the content from 
                <code>diagrams/system-architecture.mmd</code> and paste it into 
                <a href="https://mermaid.live/" target="_blank">Mermaid Live Editor</a> 
                for the best viewing experience.
            </div>
            <div class="loading">
                Diagram content available in: <strong>diagrams/system-architecture.mmd</strong>
            </div>
        </div>

        <div id="dataflow" class="diagram-section">
            <h2 class="diagram-title">Data Flow and Processing Pipeline</h2>
            <p class="diagram-description">
                Detailed flow showing how data moves through the system from input CSV files 
                through feature creation, training, model serialization, prediction, and result output. 
                Includes core data structures and key interfaces.
            </p>
            <div class="instructions">
                <strong>Instructions:</strong> To view this diagram, copy the content from 
                <code>diagrams/data-flow-pipeline.mmd</code> and paste it into 
                <a href="https://mermaid.live/" target="_blank">Mermaid Live Editor</a>.
            </div>
            <div class="loading">
                Diagram content available in: <strong>diagrams/data-flow-pipeline.mmd</strong>
            </div>
        </div>

        <div id="components" class="diagram-section">
            <h2 class="diagram-title">Component Interactions and Dependencies</h2>
            <p class="diagram-description">
                Comprehensive view of how all system components interact with each other, 
                including dependencies between packages, shared services usage, and external interfaces. 
                Shows the detailed internal architecture.
            </p>
            <div class="instructions">
                <strong>Instructions:</strong> To view this diagram, copy the content from 
                <code>diagrams/component-interactions.mmd</code> and paste it into 
                <a href="https://mermaid.live/" target="_blank">Mermaid Live Editor</a>.
            </div>
            <div class="loading">
                Diagram content available in: <strong>diagrams/component-interactions.mmd</strong>
            </div>
        </div>

        <div id="sequence" class="diagram-section">
            <h2 class="diagram-title">Usage Flow Sequence</h2>
            <p class="diagram-description">
                Step-by-step sequence diagram showing the interaction flow for both training and 
                prediction workflows. Includes user interactions, system responses, and error handling scenarios.
            </p>
            <div class="instructions">
                <strong>Instructions:</strong> To view this diagram, copy the content from 
                <code>diagrams/usage-flow-sequence.mmd</code> and paste it into 
                <a href="https://mermaid.live/" target="_blank">Mermaid Live Editor</a>.
            </div>
            <div class="loading">
                Diagram content available in: <strong>diagrams/usage-flow-sequence.mmd</strong>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Quick Start:</strong> 
                1. Go to <a href="https://mermaid.live/" target="_blank">Mermaid Live Editor</a> | 
                2. Copy content from any .mmd file | 
                3. Paste and view | 
                4. Export as PNG/SVG if needed
            </p>
            <p>
                For detailed setup instructions, see <strong>DIAGRAM_SETUP_GUIDE.md</strong>
            </p>
        </div>
    </div>

    <script>
        function showDiagram(diagramId) {
            // Hide all diagrams
            const sections = document.querySelectorAll('.diagram-section');
            sections.forEach(section => section.classList.remove('active'));
            
            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.nav button');
            buttons.forEach(button => button.classList.remove('active'));
            
            // Show selected diagram
            document.getElementById(diagramId).classList.add('active');
            document.getElementById('btn-' + diagramId).classList.add('active');
        }

        // Initialize Mermaid (though we're not using it directly here)
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
