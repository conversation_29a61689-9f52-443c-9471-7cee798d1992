sequenceDiagram
    participant User
    participant <PERSON><PERSON><PERSON> as CLI Interface
    participant Main as Main Orchestrator
    participant Utils as Utils Package
    participant Training as Training Pipeline
    participant Models as Models Package
    participant Prediction as Prediction Pipeline
    participant FS as File System

    %% Training Flow
    Note over User, FS: Training Workflow
    User->>CLI: m<PERSON><PERSON><PERSON> train --input data.csv --target class --output model.json
    CLI->>CLI: Parse and validate arguments
    CLI->>Main: Return validated config
    Main->>Utils: Load CSV data
    Utils->>FS: Read CSV file
    FS-->>Utils: Raw CSV data
    Utils-->>Main: Parsed data and headers
    
    Main->>Utils: Load feature info (optional)
    Utils->>FS: Read YAML file
    FS-->>Utils: Feature metadata
    Utils-->>Main: Feature configuration
    
    Main->>Training: Create features from data
    Training->>Models: Create FeatureStats objects
    Models-->>Training: Feature definitions
    Training-->>Main: Feature array
    
    Main->>Utils: Create CSV dataset
    Utils->>Models: Wrap data in Dataset interface
    Models-->>Utils: Dataset implementation
    Utils-->>Main: Dataset object
    
    Main->>Training: Initialize C45 splitter
    Training-->>Main: Configured splitter
    
    Main->>Training: Initialize tree builder
    Training-->>Main: Configured builder
    
    Main->>Training: Build decision tree
    Training->>Training: Recursive tree construction
    Training->>Models: Create tree nodes
    Models-->>Training: TreeNode objects
    Training->>Models: Create decision tree
    Models-->>Training: DecisionTree object
    Training-->>Main: Trained model
    
    Main->>Main: Serialize model to JSON
    Main->>FS: Save model file
    FS-->>Main: Confirmation
    Main-->>User: Training completed successfully
    
    %% Prediction Flow
    Note over User, FS: Prediction Workflow
    User->>CLI: mulberri predict --model model.json --input new_data.csv --output predictions.csv
    CLI->>CLI: Parse and validate arguments
    CLI->>Main: Return validated config
    
    Main->>Main: Load model from JSON
    Main->>FS: Read model file
    FS-->>Main: Model JSON data
    Main->>Models: Deserialize to DecisionTree
    Models-->>Main: DecisionTree object
    
    Main->>Utils: Load prediction data
    Utils->>FS: Read prediction CSV
    FS-->>Utils: Raw prediction data
    Utils->>Models: Validate against model features
    Models-->>Utils: Validation results
    Utils-->>Main: Prediction records
    
    Main->>Prediction: Validate input records
    Prediction->>Models: Check feature compatibility
    Models-->>Prediction: Validation results
    Prediction-->>Main: Validated records
    
    Main->>Prediction: Make predictions
    loop For each record
        Prediction->>Models: Traverse decision tree
        Models->>Models: Navigate tree nodes
        Models-->>Prediction: Prediction result
    end
    Prediction-->>Main: Prediction results
    
    Main->>Utils: Format and save results
    Utils->>FS: Write predictions CSV
    FS-->>Utils: Confirmation
    Utils-->>Main: Save confirmation
    Main-->>User: Prediction completed successfully
    
    %% Error Handling
    Note over User, FS: Error Handling (shown for training)
    alt Validation Error
        CLI->>User: Show validation error and help
    else Data Loading Error
        Utils->>Main: Return structured error
        Main->>User: Show data loading error
    else Training Error
        Training->>Main: Return training error
        Main->>User: Show training error with context
    else File System Error
        FS->>Main: Return I/O error
        Main->>User: Show file system error
    end
