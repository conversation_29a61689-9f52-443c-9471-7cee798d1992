flowchart TD
    %% Input Data
    CSVFile[CSV Training Data<br/>Raw tabular data]
    YAMLFile[Feature Info YAML<br/>Optional metadata]
    ModelFile[Trained Model<br/>JSON serialized]
    PredCSV[Prediction CSV<br/>New data to predict]
    
    %% Training Pipeline
    subgraph "Training Pipeline"
        direction TB
        
        %% Stage 1: Data Loading
        LoadCSV[Load CSV Data<br/>utils.ReadTrainingCSV]
        LoadYAML[Load Feature Info<br/>utils.ParseFeatureInfoFile]
        
        %% Stage 2: Feature Processing
        CreateFeatures[Create Features<br/>training.CreateFeaturesFromMetadata]
        FeatureStats[FeatureStats Objects<br/>- Name, Type, Range<br/>- Categorical Values<br/>- Column Number]
        
        %% Stage 3: Dataset Creation
        CreateDataset[Create Dataset<br/>utils.NewCSVDataset]
        Dataset[CSV Dataset<br/>Implements Dataset[string]]
        
        %% Stage 4: Tree Building
        InitSplitter[Initialize C45 Splitter<br/>training.NewC45Splitter]
        InitBuilder[Initialize Tree Builder<br/>builder.NewTreeBuilderWithInference]
        BuildTree[Build Decision Tree<br/>builder.BuildTree]
        
        %% Stage 5: Model Output
        DecisionTree[Decision Tree Model<br/>- Root TreeNode<br/>- Features<br/>- Statistics]
        SerializeModel[Serialize Model<br/>JSON marshaling]
    end
    
    %% Prediction Pipeline
    subgraph "Prediction Pipeline"
        direction TB
        
        %% Stage 1: Model Loading
        LoadModel[Load Model<br/>JSON unmarshaling]
        ValidateModel[Validate Model<br/>Structure validation]
        
        %% Stage 2: Data Loading
        LoadPredData[Load Prediction Data<br/>utils.LoadPredictionDataFromModel]
        PredRecords[Prediction Records<br/>map[string]interface{}]
        
        %% Stage 3: Prediction
        ValidateInput[Validate Input<br/>prediction.ValidateInputRecord]
        TraverseTree[Tree Traversal<br/>prediction.TraverseTree]
        
        %% Stage 4: Results
        TraversalResults[Traversal Results<br/>- Prediction<br/>- Confidence<br/>- Probabilities<br/>- Decision Path]
        FormatResults[Format Results<br/>utils.WritePredictionCSV]
    end
    
    %% Core Data Structures
    subgraph "Core Data Structures"
        direction LR
        
        TreeNode[TreeNode<br/>- IsLeaf: bool<br/>- Feature: FeatureStats<br/>- Threshold: float64<br/>- Children: map/left/right<br/>- ClassDistribution: map]
        
        SplitResult[SplitResult<br/>- Feature: FeatureStats<br/>- Threshold: float64<br/>- InfoGain: float64<br/>- LeftIndices: []int<br/>- RightIndices: []int]
        
        TraversalResult[TraversalResult<br/>- Prediction: interface{}<br/>- Confidence: float64<br/>- Probabilities: map<br/>- Path: []string<br/>- RulePath: string]
    end
    
    %% Interfaces
    subgraph "Key Interfaces"
        direction TB
        
        DatasetInterface[Dataset[T]<br/>- GetSize() int<br/>- GetFeatureValue(idx, feature)<br/>- GetTarget(idx) T<br/>- Subset(indices) Dataset[T]]
        
        SplitterInterface[Splitter[T]<br/>- FindBestSplit(ctx, dataset, features)<br/>- CalculateImpurity(dataset)]
        
        BuilderInterface[TreeBuilder[T]<br/>- BuildTree(ctx, dataset, features)]
    end
    
    %% Data Flow Connections
    CSVFile --> LoadCSV
    YAMLFile --> LoadYAML
    LoadCSV --> CreateFeatures
    LoadYAML --> CreateFeatures
    CreateFeatures --> FeatureStats
    FeatureStats --> CreateDataset
    CreateDataset --> Dataset
    Dataset --> InitSplitter
    InitSplitter --> InitBuilder
    InitBuilder --> BuildTree
    BuildTree --> DecisionTree
    DecisionTree --> SerializeModel
    SerializeModel --> ModelFile
    
    %% Prediction Flow
    ModelFile --> LoadModel
    LoadModel --> ValidateModel
    PredCSV --> LoadPredData
    LoadPredData --> PredRecords
    ValidateModel --> TraverseTree
    PredRecords --> ValidateInput
    ValidateInput --> TraverseTree
    TraverseTree --> TraversalResults
    TraversalResults --> FormatResults
    FormatResults --> OutputCSV[Prediction Results CSV]
    
    %% Interface Implementations
    Dataset -.-> DatasetInterface
    InitSplitter -.-> SplitterInterface
    InitBuilder -.-> BuilderInterface
    
    %% Core Structure Usage
    BuildTree --> TreeNode
    BuildTree --> SplitResult
    TraverseTree --> TraversalResult
    
    %% Styling
    classDef inputFile fill:#e3f2fd
    classDef process fill:#f3e5f5
    classDef dataStruct fill:#e8f5e8
    classDef interface fill:#fff3e0
    classDef output fill:#fce4ec
    
    class CSVFile,YAMLFile,ModelFile,PredCSV inputFile
    class LoadCSV,LoadYAML,CreateFeatures,CreateDataset,InitSplitter,InitBuilder,BuildTree,SerializeModel,LoadModel,ValidateModel,LoadPredData,ValidateInput,TraverseTree,FormatResults process
    class FeatureStats,Dataset,DecisionTree,PredRecords,TraversalResults,TreeNode,SplitResult,TraversalResult dataStruct
    class DatasetInterface,SplitterInterface,BuilderInterface interface
    class OutputCSV output
