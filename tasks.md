# Decision Tree Refactor - Task Backlog

## Epic: Mulberri Decision Tree Refactoring
**Goal**: Refactor existing decision tree implementation for better maintainability, performance, and memory efficiency using cleaner interfaces and design patterns.

**Current Codebase Context**:
- **Existing CLI**: Full-featured CLI in `cmd/mulberri/cli/` with training/prediction commands, comprehensive flag parsing, and validation
- **Current Models**: `pkg/models/` contains DecisionTree, TreeNode, Feature structures with JSON serialization
- **Training Pipeline**: `internals/training/` has C45Splitter, Dataset interface, and TreeBuilder with Builder pattern
- **Prediction System**: `internals/prediction/` provides tree traversal, batch processing, and data validation
- **Data Loading**: `internals/utils/` handles CSV loading, feature creation, type detection, and validation
- **Current Interfaces**: Dataset[T], Splitter[T], TreeConstructor[T] already exist with generic type support

**Timeline**:
- Sprint 1: Training functionality
- Sprint 2: Prediction functionality
- Mid-September: Complete decision tree ready
- End September: All bugs resolved

---

## Phase 1: Foundation & Setup

### Task 1.1: Create Base Refactor Branch
**Priority**: Critical
**Effort**: 1 story point
**Description**: Create and setup the base branch for all refactoring work

**Current Context**: Repository is at `/home/<USER>/projects/mulberri` with existing main branch containing full implementation

**Acceptance Criteria**:
- [ ] Create `refactor/mulberri-v2` branch from main
- [ ] Ensure all team members have access
- [ ] Document branch strategy in README
- [ ] Setup branch protection rules
- [ ] Verify all existing tests pass on new branch
- [ ] Confirm CI/CD pipeline works with new branch

### Task 1.2: Project Structure Setup
**Priority**: High
**Effort**: 2 story points
**Description**: Implement the new directory structure as specified

**Current Context**:
- Existing structure: `cmd/mulberri/`, `pkg/models/`, `internals/training/`, `internals/prediction/`, `internals/utils/`
- Current packages are well-organized but need consolidation under new `internal/` structure
- Existing CLI in `cmd/mulberri/cli/` with comprehensive functionality

**Acceptance Criteria**:
- [ ] Create `internal/` directory with sub-directories:
  - `config/` (defaults, training, prediction, validation)
  - `data/` (features, dataset subdirectories)
  - `tree/` (node, tree, traversal)
  - `training/` (builder, splitter, impurity, etc.)
  - `prediction/` (predictor, batch, validation)
  - `evaluation/` (metrics, confusion matrix, stats)
  - `io/` (formats, persistence, cli)
  - `utils/` (logger)
- [ ] Move existing code to temporary `legacy/` folder
- [ ] Update go.mod if needed
- [ ] Create placeholder files with package declarations
- [ ] Ensure all import paths are documented for migration
- [ ] Verify no circular dependencies in new structure

### Task 1.3: Documentation Standards Implementation
**Priority**: High
**Effort**: 3 story points
**Description**: Implement Berrijam Go commenting standards across new structure

**Current Context**:
- Existing code has good documentation in `cmd/mulberri/cli/`, `internals/training/builder/`, and `pkg/models/`
- Current packages follow Go conventions but need standardization
- Examples exist in `internals/prediction/traversal.md` and package READMEs

**Acceptance Criteria**:
- [ ] Create documentation template files
- [ ] Implement commenting standards for:
  - Function documentation (Args, Returns, Examples)
  - Struct documentation (Security, Relationships, Side effects)
  - Interface documentation
  - Constants & Variables
  - Error Types
  - Package documentation
- [ ] Create example files showing proper documentation
- [ ] Setup linting rules for documentation compliance
- [ ] Audit existing documentation and create improvement plan
- [ ] Ensure all public APIs have comprehensive documentation

---

## Phase 2: Core Data Structures

### Task 2.1: FeatureColumn Interface Implementation
**Priority**: Critical
**Effort**: 5 story points
**Description**: Implement the core FeatureColumn interface and its implementations

**Current Context**:
- Existing `pkg/models/Feature` struct with Type, CategoricalValues, NumericRange
- Current Dataset interface: `GetFeatureValue(sampleIdx int, feature *models.Feature) (interface{}, error)`
- Type system: NumericFeature, CategoricalFeature, DateTimeFeature already defined
- CSV loading in `internals/utils/` handles type conversion and validation

**Acceptance Criteria**:
- [ ] Define `FeatureColumn` interface with methods:
  - `GetValue(physicalIndex int) (interface{}, error)`
  - `GetNumericalValue(physicalIndex int) (float64, error)`
  - `GetSize() int`
  - `GetType() FeatureType`
  - `IsNumerical() bool`
- [ ] Implement `IntColumn` struct with null mask handling
- [ ] Implement `FloatColumn` struct with null mask handling
- [ ] Implement `StringColumn` struct with null mask handling
- [ ] Add comprehensive unit tests for all implementations
- [ ] Document with proper commenting standards
- [ ] Ensure compatibility with existing Feature struct
- [ ] Benchmark performance vs current implementation

### Task 2.2: Dataset Structure Implementation
**Priority**: Critical
**Effort**: 5 story points
**Description**: Create the main Dataset structure for full data storage

**Current Context**:
- Existing Dataset[T] interface in `internals/training/types.go` with GetSize(), GetFeatureValue(), GetTarget(), Subset()
- Current implementations: TypedCSVDataset, CSVDataset in `internals/utils/dataset.go`
- Mixed type storage already exists: `features [][]interface{}` with string, float64, int64
- Zero-copy subsetting implemented via index arrays

**Acceptance Criteria**:
- [ ] Implement `Dataset[T comparable]` struct with:
  - Separate storage maps for each type (intColumns, floatColumns, stringColumns)
  - Target values array
  - Feature metadata and ordering
  - Total size tracking
- [ ] Implement core methods:
  - `CreateView(activeIndices []int) *DatasetView[T]`
  - `GetColumn(featureName string) (FeatureColumn, error)`
- [ ] Add validation for data consistency
- [ ] Implement proper error handling
- [ ] Add comprehensive unit tests
- [ ] Maintain compatibility with existing Dataset[T] interface
- [ ] Ensure memory efficiency matches current zero-copy approach

### Task 2.3: DatasetView Implementation
**Priority**: Critical
**Effort**: 6 story points
**Description**: Implement DatasetView for memory-efficient subset operations

**Current Context**:
- Current Subset() method creates new Dataset with index arrays: `Subset(indices []int) Dataset[T]`
- TypedCSVDataset already uses index-based views for memory efficiency
- Target distribution calculation exists in C45Splitter for entropy/gini calculations
- Split operations currently handled in C45Splitter with LeftIndices/RightIndices

**Acceptance Criteria**:
- [ ] Implement `DatasetView[T comparable]` struct with:
  - Reference to full dataset
  - Active indices array
  - Size tracking
  - Cached target distribution with dirty flag
- [ ] Implement core methods:
  - `GetFeatureValue(logicalIndex int, featureName string)`
  - `GetTarget(logicalIndex int)`
  - `GetTargetDistribution()`
  - `CreateChildView(logicalIndices []int)`
- [ ] Implement split methods:
  - `SplitByNumericalThreshold()`
  - `SplitByStringValue()`
  - `SplitByCondition()`
- [ ] Add caching mechanism for target distributions
- [ ] Add comprehensive unit tests
- [ ] Performance testing for memory efficiency
- [ ] Ensure compatibility with existing Subset() interface
- [ ] Benchmark against current index-based approach

---

## Phase 3: Split Evaluation System

### Task 3.1: Split Evaluator Interface & Factory
**Priority**: High
**Effort**: 4 story points
**Description**: Implement the Strategy pattern for split evaluation

**Current Context**:
- Existing C45Splitter in `internals/training/split.go` with FindBestSplit() method
- Current SplitResult[T] struct with Feature, Threshold, InfoGain, GainRatio, LeftIndices, RightIndices
- Parallel feature evaluation already implemented with worker pools
- Separate handling for numeric vs categorical features in current splitter

**Acceptance Criteria**:
- [ ] Define `SplitEvaluator[T comparable]` interface
- [ ] Implement `SplitEvaluatorFactory[T comparable]` with:
  - `CreateEvaluator(column FeatureColumn) SplitEvaluator[T]`
- [ ] Define `SplitCandidate` struct with unified split representation
- [ ] Implement `SplitCondition` interface for evaluation
- [ ] Add unit tests for factory pattern implementation
- [ ] Ensure compatibility with existing SplitResult[T] structure
- [ ] Maintain parallel processing capabilities from current implementation

### Task 3.2: Numerical Split Evaluator
**Priority**: High
**Effort**: 5 story points
**Description**: Implement numerical splitting for int and float features

**Current Context**:
- Current C45Splitter handles numeric splits with sorting and threshold evaluation
- Existing numericValue[T] and integerValue[T] structs for sorted pairs
- Object pools for memory efficiency: targetDistPool, indicesPool
- Performance optimizations already in place for large datasets

**Acceptance Criteria**:
- [ ] Implement `NumericalSplitEvaluator[T comparable]` with:
  - `EvaluateSplits()` method working on DatasetView
  - `getSortedNumericalPairs()` helper method
- [ ] Implement threshold-based splitting logic
- [ ] Optimize sorting algorithm for performance
- [ ] Add comprehensive unit tests with various data scenarios
- [ ] Performance benchmarking
- [ ] Maintain object pooling for memory efficiency
- [ ] Ensure performance matches or exceeds current implementation

### Task 3.3: Categorical Split Evaluator
**Priority**: High
**Effort**: 5 story points
**Description**: Implement categorical splitting for string features

**Current Context**:
- Current C45Splitter handles categorical splits with binary partitioning
- Existing logic for string value distribution calculation
- Current approach creates binary splits (value vs all others) for categorical features
- Performance considerations for features with many categorical values

**Acceptance Criteria**:
- [ ] Implement `CategoricalSplitEvaluator[T comparable]` with:
  - `EvaluateSplits()` method working on DatasetView
  - `getStringJointDistribution()` helper method
  - `calculateComplementDistribution()` helper method
- [ ] Implement value-based binary splitting logic
- [ ] Add comprehensive unit tests
- [ ] Performance optimization for large categorical features
- [ ] Maintain binary splitting approach from current implementation
- [ ] Handle edge cases for categorical features with many unique values

### Task 3.4: Impurity Calculations
**Priority**: High
**Effort**: 3 story points
**Description**: Implement impurity calculation strategies

**Current Context**:
- Current C45Splitter implements entropy and gain ratio calculations
- ImpurityCriterion enum exists: EntropyImpurity, GiniImpurity, MSEImpurity
- Mathematical functions already implemented for information gain and split information
- Current implementation handles edge cases (empty sets, pure nodes)

**Acceptance Criteria**:
- [ ] Define `ImpurityCalculator[T comparable]` interface
- [ ] Implement entropy calculator for information gain
- [ ] Add input validation and error handling
- [ ] Create utility functions for impurity calculations
- [ ] Add unit tests with mathematical verification
- [ ] Support all existing impurity criteria (Entropy, Gini, MSE)
- [ ] Ensure mathematical accuracy matches current implementation

---

## Phase 4: Tree Structure & Nodes

### Task 4.1: Node Interface & Serialization
**Priority**: High
**Effort**: 4 story points
**Description**: Design unified node interface with serialization support

**Current Context**:
- Existing TreeNode struct in `pkg/models/tree_node.go` with Type, Feature, Threshold, Categories, Left, Right
- JSON serialization already implemented with struct tags
- NodeType enum: DecisionNode, LeafNode
- Current serialization supports both numerical and categorical splits

**Acceptance Criteria**:
- [ ] Define `Node` interface with methods:
  - Core node operations (IsLeaf, GetSamples, etc.)
  - Serialization support (GetNodeType, ToSerializable)
- [ ] Implement `SerializableNode` struct for JSON compatibility
- [ ] Add validation methods
- [ ] Create helper functions for node statistics
- [ ] Add comprehensive unit tests
- [ ] Maintain compatibility with existing TreeNode JSON format
- [ ] Ensure backward compatibility for model loading/saving

### Task 4.2: Leaf Node Implementation
**Priority**: High
**Effort**: 3 story points
**Description**: Implement terminal nodes with prediction capability

**Current Context**:
- Current TreeNode with Type=LeafNode contains Prediction, ClassDistribution, Confidence
- Leaf nodes created in TreeBuilder with target distribution and majority class
- Confidence calculation based on class distribution ratios
- JSON serialization already working for leaf nodes

**Acceptance Criteria**:
- [ ] Implement `LeafNode` struct with:
  - Prediction value
  - Class distribution
  - Confidence calculation
- [ ] Add `NewLeafNode()` constructor with validation
- [ ] Implement all Node interface methods
- [ ] Add serialization methods
- [ ] Add comprehensive unit tests
- [ ] Maintain compatibility with existing leaf node structure
- [ ] Ensure confidence calculation matches current implementation

### Task 4.3: Decision Node Implementation
**Priority**: High
**Effort**: 5 story points
**Description**: Implement internal nodes with splitting logic

**Current Context**:
- Current TreeNode with Type=DecisionNode contains Feature, Threshold, Categories map, Left, Right
- Binary splits for numerical features (Left/Right children)
- Multi-way splits for categorical features (Categories map)
- Feature information stored as *models.Feature reference

**Acceptance Criteria**:
- [ ] Implement `DecisionNode` struct with:
  - Feature information
  - Split value storage
  - Children management (unified for both binary and multi-way)
  - Class distribution tracking
- [ ] Add child management methods:
  - `SetChild()`, `GetChild()`, `GetAllChildren()`
  - Convenience methods for binary splits (SetLeftChild, SetRightChild)
- [ ] Implement serialization methods
- [ ] Add comprehensive unit tests
- [ ] Test both numerical and categorical splitting scenarios
- [ ] Maintain compatibility with existing TreeNode structure
- [ ] Support both binary and multi-way split representations

### Task 4.4: Tree Container & Metadata
**Priority**: Medium
**Effort**: 3 story points
**Description**: Implement DecisionTree container with metadata

**Current Context**:
- Existing DecisionTree struct in `pkg/models/tree.go` with Root, Features, FeaturesByIndex, TargetType, Config
- TreeConfig with MaxDepth, MinSamples, TargetType, Criterion
- JSON serialization/deserialization already implemented
- Tree statistics: NodeCount, LeafCount, Depth tracking

**Acceptance Criteria**:
- [ ] Implement `DecisionTree` struct with:
  - Root node reference
  - Feature metadata
  - Classes information
  - Tree metadata (version, algorithm, parameters)
- [ ] Implement `TreeMetadata` for tracking tree statistics
- [ ] Add tree statistics calculation methods
- [ ] Implement tree serialization/deserialization
- [ ] Add validation methods
- [ ] Maintain compatibility with existing DecisionTree JSON format
- [ ] Preserve existing TreeConfig structure and functionality

---

## Phase 5: Configuration & CLI System

### Task 5.1: Configuration System
**Priority**: Medium
**Effort**: 4 story points
**Description**: Implement simplified configuration with defaults

**Current Context**:
- Existing CLI Config struct in `cmd/mulberri/cli/config.go` with comprehensive validation
- Current defaults: MaxDepth=10, MinSamples=2, MinSamplesLeaf=1, Criterion="entropy"
- Full validation system with structured error types (CLIError)
- Environment variable support documented but not implemented

**Acceptance Criteria**:
- [ ] Create `Config` struct with all CLI parameters
- [ ] Implement `GetDefaults()` with specified default values:
  - MaxDepth: 10
  - MinSamples: 20
  - Criterion: "entropy"
- [ ] Add configuration validation
- [ ] Implement environment-specific configs
- [ ] Add unit tests for configuration handling
- [ ] Maintain compatibility with existing CLI Config structure
- [ ] Preserve existing validation logic and error handling

### Task 5.2: CLI Parser Implementation
**Priority**: Medium
**Effort**: 4 story points
**Description**: Create command-line interface with proper argument parsing

**Current Context**:
- Existing ParseFlags() function in `cmd/mulberri/cli/flags.go` with comprehensive flag parsing
- Full CLI implementation with train/predict commands, help, version, verbose flags
- Custom usage function with detailed help text and examples
- Comprehensive validation and error handling already implemented

**Acceptance Criteria**:
- [ ] Implement `ParseArgs()` function with flag definitions
- [ ] Add custom usage message with examples
- [ ] Implement command validation (train/predict)
- [ ] Add help and version flags
- [ ] Error handling for invalid arguments
- [ ] Add integration tests for CLI parsing
- [ ] Maintain compatibility with existing CLI interface
- [ ] Preserve all current flags and functionality

### Task 5.3: Feature Info YAML Support
**Priority**: Medium
**Effort**: 3 story points
**Description**: Implement YAML-based feature metadata loading

**Current Context**:
- Existing YAML feature info support in `internals/utils/feature_info_csv_loader.go`
- FeatureInfoParser with ParseFeatureInfoFile() method already implemented
- YAML schema supports type, handle_as, and other metadata fields
- Integration with CSV loading and feature creation already working

**Acceptance Criteria**:
- [ ] Design YAML schema for feature definitions:
  - `type`: nominal, numeric, date, datetime, binary, time
  - `handle_as`: integer, float, string
- [ ] Implement YAML parser in `data/features/loader.go`
- [ ] Add validation for feature type compatibility
- [ ] Create example feature info files
- [ ] Add unit tests for YAML loading
- [ ] Maintain compatibility with existing YAML feature info format
- [ ] Preserve existing FeatureInfoParser functionality

---

## Phase 6: Training Implementation

### Task 6.1: Tree Builder Core
**Priority**: Critical
**Effort**: 6 story points
**Description**: Implement main tree building orchestration using Builder pattern

**Current Context**:
- Existing TreeBuilder[T] in `internals/training/builder/tree_builder.go` with full Builder pattern implementation
- Interface-based design with NodeBuilder, StatisticsCalculator, StoppingCriteria, SplittingStrategy
- Recursive tree building with BuildTree() method already implemented
- Progress tracking, logging, and comprehensive error handling already in place

**Acceptance Criteria**:
- [ ] Create `TreeBuilder` struct implementing Builder pattern
- [ ] Implement recursive tree building algorithm:
  - Stopping criteria evaluation (max depth, min samples)
  - Best split selection across all features
  - Child view creation and recursive calls
- [ ] Add progress tracking and logging
- [ ] Implement proper error handling
- [ ] Add comprehensive unit tests with various datasets
- [ ] Performance benchmarking
- [ ] Maintain compatibility with existing TreeBuilder interface
- [ ] Preserve existing Builder pattern and interface-based design

### Task 6.2: Training Orchestration
**Priority**: High
**Effort**: 4 story points
**Description**: Create training workflow coordination

**Current Context**:
- Existing training orchestration in `cmd/mulberri/main.go` handleTrainCommand()
- Complete workflow: CSV loading, feature creation, dataset creation, tree building, model saving
- Progress reporting with ProgressReporter and stage-based updates
- Integration with utils package for data loading and validation

**Acceptance Criteria**:
- [ ] Implement training service with:
  - Data loading and validation
  - Feature type detection and conversion
  - Tree building coordination
  - Model serialization
- [ ] Add training progress reporting using Observer pattern
- [ ] Implement training metrics collection
- [ ] Add comprehensive integration tests
- [ ] Error handling and recovery
- [ ] Maintain compatibility with existing training workflow
- [ ] Preserve existing progress reporting and error handling

### Task 6.3: Stopping Criteria Implementation
**Priority**: High
**Effort**: 3 story points
**Description**: Implement configurable stopping criteria

**Current Context**:
- Existing StoppingCriteria interface in TreeBuilder with ShouldStop() method
- Current implementation checks MaxDepth, MinSamples, and pure node conditions
- Configuration through TreeConfig with MaxDepth, MinSamples parameters
- Integration with recursive tree building already implemented

**Acceptance Criteria**:
- [ ] Create stopping criteria evaluator with:
  - Maximum depth checking
  - Minimum samples validation
  - Pure node detection
  - Information gain threshold (future)
- [ ] Make criteria configurable through Config
- [ ] Add unit tests for all stopping conditions
- [ ] Integration with tree builder
- [ ] Maintain compatibility with existing StoppingCriteria interface
- [ ] Preserve existing stopping logic and configuration

---

## Phase 7: Data Loading & Validation

### Task 7.1: CSV Data Loader
**Priority**: High
**Effort**: 4 story points
**Description**: Implement robust CSV loading with type conversion

**Current Context**:
- Existing CSV loading in `internals/utils/csv_loader.go` with ReadTrainingCSV(), ReadPredictionCSV()
- Type detection and conversion in `internals/training/feature_creation.go`
- Comprehensive validation with structured error types (CSVError, FeatureInfoError)
- Support for quoted fields, special characters, and configurable NA values

**Acceptance Criteria**:
- [ ] Create CSV loader in `data/dataset/loader.go`
- [ ] Implement type detection and conversion:
  - String to int64 conversion
  - String to float64 conversion
  - String handling with proper escaping
- [ ] Add missing value detection and rejection
- [ ] Implement data validation pipeline using Chain of Responsibility
- [ ] Add comprehensive unit tests with various CSV formats
- [ ] Performance optimization for large files
- [ ] Maintain compatibility with existing CSV loading functionality
- [ ] Preserve existing error handling and validation logic

### Task 7.2: Data Validation Pipeline
**Priority**: High
**Effort**: 3 story points
**Description**: Create comprehensive data validation using Chain of Responsibility pattern

**Current Context**:
- Existing validation in `internals/prediction/data_validator.go` with ValidatePredictionRecords()
- Feature validation by type (numeric, categorical, datetime) already implemented
- Structured error reporting with ValidationError type
- Integration with CSV loading and feature creation processes

**Acceptance Criteria**:
- [ ] Implement validation chain with:
  - Feature consistency validation
  - Missing value detection
  - Type compatibility checking
  - Target column validation
- [ ] Create specific validator classes for each check
- [ ] Add detailed error reporting
- [ ] Integration with data loading process
- [ ] Add unit tests for each validator
- [ ] Maintain compatibility with existing validation functionality
- [ ] Preserve existing ValidationError structure and reporting

---

## Phase 8: Prediction System

### Task 8.1: Prediction Engine Core
**Priority**: High
**Effort**: 4 story points
**Description**: Implement tree traversal for predictions using State pattern

**Current Context**:
- Existing prediction engine in `internals/prediction/tree_traversal.go` with TraverseTree()
- Multiple traversal strategies: recursive, iterative, automatic selection
- Missing value handling with configurable strategies
- Confidence scoring and probability calculation from class distributions

**Acceptance Criteria**:
- [ ] Create prediction service with:
  - Single instance prediction
  - Tree traversal logic for all node types
  - Missing value handling during prediction
- [ ] Implement State pattern for traversal states
- [ ] Add confidence scoring
- [ ] Comprehensive unit tests with various tree structures
- [ ] Performance optimization
- [ ] Maintain compatibility with existing TraverseTree functionality
- [ ] Preserve existing traversal strategies and missing value handling

### Task 8.2: Batch Prediction Support
**Priority**: Medium
**Effort**: 3 story points
**Description**: Enable efficient batch predictions

**Current Context**:
- Existing PredictBatch() function in `internals/prediction/tree_traversal.go`
- Memory-efficient processing with progress reporting every 100 records
- CSV output formatting in `cmd/mulberri/main.go` savePredictionResults()
- Integration with prediction workflow and error handling

**Acceptance Criteria**:
- [ ] Implement batch prediction with:
  - Memory-efficient processing
  - Progress reporting
  - Error handling for individual failures
- [ ] Add CSV output formatting
- [ ] Implement prediction result aggregation
- [ ] Add performance benchmarking
- [ ] Integration tests with large datasets
- [ ] Maintain compatibility with existing PredictBatch functionality
- [ ] Preserve existing progress reporting and CSV output format

---

## Phase 9: I/O & Persistence

### Task 9.1: Model Serialization System
**Priority**: High
**Effort**: 4 story points
**Description**: Implement JSON-based model persistence

**Current Context**:
- Existing JSON serialization in `pkg/models/tree.go` and `pkg/models/tree_node.go`
- Complete serialization/deserialization with struct tags for all fields
- Model loading/saving in `cmd/mulberri/main.go` with loadModel() and saveModel()
- Feature metadata inclusion and backwards compatibility already implemented

**Acceptance Criteria**:
- [ ] Create tree serialization with:
  - JSON format as specified in requirements
  - Feature metadata inclusion
  - Backwards compatibility considerations
- [ ] Implement deserialization with validation
- [ ] Add model versioning support
- [ ] Error handling for corrupted models
- [ ] Add comprehensive tests with complex trees
- [ ] Maintain compatibility with existing JSON format
- [ ] Preserve existing model loading/saving functionality

### Task 9.2: Unified I/O System
**Priority**: Medium
**Effort**: 3 story points
**Description**: Create format-agnostic I/O system using Factory pattern

**Current Context**:
- Existing I/O operations distributed across utils package (CSV), models package (JSON), and CLI
- CSV reading/writing in `internals/utils/csv_loader.go` and `internals/utils/prediction_loader.go`
- JSON model persistence in `pkg/models/` with marshal/unmarshal
- YAML configuration loading in `internals/utils/feature_info_csv_loader.go`

**Acceptance Criteria**:
- [ ] Implement I/O factory for:
  - CSV reading/writing
  - JSON model persistence
  - YAML configuration loading
- [ ] Add file format detection
- [ ] Error handling for file operations
- [ ] Add unit tests for all formats
- [ ] Consolidate existing I/O operations under unified interface
- [ ] Maintain compatibility with existing file format handling

---

## Phase 10: Evaluation & Metrics

### Task 10.1: Performance Metrics
**Priority**: Medium
**Effort**: 3 story points
**Description**: Implement evaluation metrics for model assessment

**Current Context**:
- Basic tree statistics tracking in DecisionTree: NodeCount, LeafCount, Depth
- Confidence calculation in prediction results
- No comprehensive evaluation metrics currently implemented
- Performance tracking in training and prediction workflows

**Acceptance Criteria**:
- [ ] Create metrics calculator with:
  - Accuracy calculation
  - Precision, Recall, F1-score
  - Confusion matrix generation
- [ ] Add cross-validation support structure
- [ ] Node-level and tree-level statistics
- [ ] Add comprehensive unit tests
- [ ] Integrate with existing tree statistics tracking
- [ ] Provide evaluation metrics for model assessment workflows

### Task 10.2: Logging & Monitoring
**Priority**: Medium
**Effort**: 2 story points
**Description**: Implement structured logging system

**Current Context**:
- Existing logger package in `pkg/logger/` with Info, Debug, Error methods
- Verbose flag support in CLI with progress reporting
- Integration with training and prediction processes throughout codebase
- Structured logging already used in TreeBuilder and other components

**Acceptance Criteria**:
- [ ] Create logger utility with:
  - Different log levels (Info, Debug, Error)
  - File-based logging (mulberri-train.log, mulberri-predict.log)
  - Structured log format
- [ ] Add verbose flag support
- [ ] Integration with training and prediction processes
- [ ] Log rotation handling (by external wrapper)
- [ ] Enhance existing logger package with file-based logging
- [ ] Maintain compatibility with existing logging calls

---

## Phase 11: Integration & Testing

### Task 11.1: Integration Testing Suite
**Priority**: High
**Effort**: 5 story points
**Description**: Create comprehensive integration tests
- [ ] End-to-end training tests with real datasets
- [ ] End-to-end prediction tests  
- [ ] CLI integration tests
- [ ] Performance regression tests
- [ ] Memory usage validation tests
- [ ] Cross-platform compatibility tests

### Task 11.2: Performance Benchmarking
**Priority**: Medium
**Effort**: 3 story points  
**Description**: Establish performance baselines and optimization
- [ ] Create benchmark suite comparing old vs new implementation
- [ ] Memory usage profiling
- [ ] Training time benchmarks
- [ ] Prediction speed benchmarks
- [ ] Scalability testing with large datasets
- [ ] Optimization recommendations

### Task 11.3: Code Migration & Legacy Cleanup
**Priority**: Low
**Effort**: 2 story points
**Description**: Final cleanup and migration
- [ ] Remove legacy code from temporary folders
- [ ] Update all import statements
- [ ] Final documentation review
- [ ] Code review and cleanup
- [ ] Update CI/CD pipelines if needed

---

## Phase 12: Documentation & Deployment

### Task 12.1: Comprehensive Documentation
**Priority**: Medium
**Effort**: 4 story points
**Description**: Create user and developer documentation
- [ ] Update root README with new structure
- [ ] Create developer documentation in `/docs`
- [ ] Add usage examples and tutorials  
- [ ] API documentation generation
- [ ] Performance guidelines
- [ ] Troubleshooting guide

### Task 12.2: Release Preparation
**Priority**: Low
**Effort**: 2 story points  
**Description**: Prepare for production release
- [ ] Version tagging and release notes
- [ ] Deployment scripts update
- [ ] Configuration file templates
- [ ] Final integration testing
- [ ] Production readiness checklist
- [ ] Merge to main branch preparation

---

## Success Criteria

**Sprint 1 Completion (Training)**:
- [ ] Complete data structures (Tasks 2.1-2.3)
- [ ] Split evaluation system (Tasks 3.1-3.4)  
- [ ] Tree structures (Tasks 4.1-4.4)
- [ ] Training implementation (Tasks 6.1-6.2)
- [ ] Data loading (Tasks 7.1-7.2)

**Sprint 2 Completion (Prediction)**:
- [ ] Prediction system (Tasks 8.1-8.2)
- [ ] I/O system (Tasks 9.1-9.2)  
- [ ] CLI system (Tasks 5.1-5.3)
- [ ] Basic evaluation (Task 10.1)

**Mid-September (Complete System)**:
- [ ] All core functionality working
- [ ] Performance benchmarks meeting targets
- [ ] Integration tests passing
- [ ] Documentation complete

**End-September (Bug-Free)**:
- [ ] All integration tests passing
- [ ] Performance optimization complete
- [ ] Production deployment ready
- [ ] Legacy code removed

---

## Migration Strategy & Compatibility

**Current Implementation Assets**:
- Comprehensive CLI with full validation and error handling
- Complete training pipeline with C45 algorithm and Builder pattern
- Robust prediction system with multiple traversal strategies
- Extensive data loading and validation infrastructure
- JSON serialization and YAML configuration support

**Migration Approach**:
- **Phase 1**: Create new interfaces while maintaining existing implementations
- **Phase 2**: Implement new structures with compatibility layers
- **Phase 3**: Gradual migration with side-by-side testing
- **Phase 4**: Remove legacy code after full validation

**Compatibility Requirements**:
- All existing CLI commands and flags must work unchanged
- JSON model format must remain backward compatible
- Performance must match or exceed current implementation
- All existing tests must pass with new implementation

---

## Risk Mitigation

**High Risk Items**:
- Memory efficiency of DatasetView implementation (Task 2.3)
- Performance of split evaluation algorithms (Tasks 3.2-3.3)
- Serialization/deserialization reliability (Task 9.1)
- Maintaining backward compatibility during refactor

**Mitigation Strategies**:
- Early performance testing and profiling
- Incremental development with continuous testing
- Regular code reviews focusing on memory management
- Fallback plans for critical performance bottlenecks
- Comprehensive integration testing with existing workflows
- Side-by-side performance benchmarking