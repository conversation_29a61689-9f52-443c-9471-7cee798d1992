# Mulberri System Architecture Diagrams - Setup Guide

This guide provides multiple ways to view and work with the Mermaid diagrams for the Mulberri decision tree system architecture.

## 📁 Files Included

- `SYSTEM_ARCHITECTURE.md` - Comprehensive system documentation
- `diagrams/system-architecture.mmd` - High-level system architecture
- `diagrams/data-flow-pipeline.mmd` - Data flow and processing pipeline
- `diagrams/component-interactions.mmd` - Component interactions and dependencies
- `diagrams/usage-flow-sequence.mmd` - Usage flow sequence diagram

## 🌐 Online Viewing Options

### Option 1: Mermaid Live Editor (Recommended)
1. Go to [https://mermaid.live/](https://mermaid.live/)
2. Copy the content from any `.mmd` file
3. Paste it into the editor
4. View the rendered diagram instantly
5. Export as PNG, SVG, or PDF

### Option 2: GitHub (if uploading to GitHub)
GitHub natively renders Mermaid diagrams in markdown files. Simply:
1. Upload the files to a GitHub repository
2. View the `.mmd` files directly in GitHub
3. Or embed them in markdown using:
```markdown
```mermaid
[paste diagram content here]
```
```

### Option 3: GitLab
GitLab also supports Mermaid diagrams natively in markdown files.

## 💻 Local Setup Options

### Option 1: VS Code with Mermaid Extension
1. Install [Visual Studio Code](https://code.visualstudio.com/)
2. Install the "Mermaid Preview" extension by Mermaid
3. Open any `.mmd` file
4. Use `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) and search for "Mermaid: Preview"
5. View the rendered diagram in a side panel

### Option 2: Mermaid CLI (Command Line)
1. Install Node.js from [https://nodejs.org/](https://nodejs.org/)
2. Install Mermaid CLI:
```bash
npm install -g @mermaid-js/mermaid-cli
```
3. Convert diagrams to images:
```bash
# Convert to PNG
mmdc -i diagrams/system-architecture.mmd -o system-architecture.png

# Convert to SVG
mmdc -i diagrams/system-architecture.mmd -o system-architecture.svg

# Convert to PDF
mmdc -i diagrams/system-architecture.mmd -o system-architecture.pdf
```

### Option 3: Local Web Server
1. Create an HTML file with Mermaid.js:
```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
    <div class="mermaid">
        <!-- Paste diagram content here -->
    </div>
    <script>
        mermaid.initialize({startOnLoad:true});
    </script>
</body>
</html>
```
2. Open in a web browser

## 📱 Mobile/Tablet Viewing

### Option 1: Mermaid Live Editor
- Works on mobile browsers
- Go to [https://mermaid.live/](https://mermaid.live/)
- Copy/paste diagram content

### Option 2: GitHub Mobile App
- Upload files to GitHub
- View directly in the GitHub mobile app

## 🔧 Integration with Documentation Tools

### Notion
1. Use Mermaid Live Editor to export as PNG/SVG
2. Upload the image to Notion

### Confluence
1. Install a Mermaid plugin for Confluence
2. Or export diagrams as images and embed

### Google Docs/Slides
1. Export diagrams as PNG/SVG from Mermaid Live Editor
2. Insert as images

### Microsoft Office
1. Export diagrams as PNG/SVG
2. Insert into Word/PowerPoint documents

## 🎨 Customization Options

### Themes
You can customize the appearance by adding theme configuration:
```mermaid
%%{init: {'theme':'dark'}}%%
graph TB
    [your diagram content]
```

Available themes: `default`, `dark`, `forest`, `neutral`

### Colors
Modify the styling sections in the diagrams to change colors:
```mermaid
classDef customClass fill:#f9f,stroke:#333,stroke-width:2px
class NodeName customClass
```

## 📋 Quick Start Checklist

For immediate viewing:
- [ ] Go to [Mermaid Live Editor](https://mermaid.live/)
- [ ] Open `diagrams/system-architecture.mmd`
- [ ] Copy all content
- [ ] Paste into Mermaid Live Editor
- [ ] View rendered diagram

For local development:
- [ ] Install VS Code
- [ ] Install Mermaid Preview extension
- [ ] Open `.mmd` files in VS Code
- [ ] Use preview command

For documentation:
- [ ] Export diagrams as PNG/SVG from Mermaid Live Editor
- [ ] Include in your documentation platform
- [ ] Reference the `SYSTEM_ARCHITECTURE.md` for detailed explanations

## 🔍 Diagram Descriptions

### 1. System Architecture (`system-architecture.mmd`)
- **Purpose**: High-level overview of system components
- **Best for**: Understanding overall structure and component relationships
- **Key elements**: CLI layer, core packages, training/prediction pipelines

### 2. Data Flow Pipeline (`data-flow-pipeline.mmd`)
- **Purpose**: Shows how data flows through the system
- **Best for**: Understanding data transformation and processing stages
- **Key elements**: Input data, processing stages, output formats

### 3. Component Interactions (`component-interactions.mmd`)
- **Purpose**: Detailed view of component dependencies
- **Best for**: Understanding how components communicate
- **Key elements**: Dependencies, shared services, external interfaces

### 4. Usage Flow Sequence (`usage-flow-sequence.mmd`)
- **Purpose**: Step-by-step interaction flow
- **Best for**: Understanding user workflows and system behavior
- **Key elements**: Training workflow, prediction workflow, error handling

## 💡 Tips for Best Experience

1. **Start with System Architecture** - Get the big picture first
2. **Use Mermaid Live Editor** - Fastest way to view diagrams
3. **Export for Sharing** - PNG/SVG exports work everywhere
4. **Read Documentation** - Combine diagrams with `SYSTEM_ARCHITECTURE.md`
5. **Zoom and Pan** - Most viewers support zooming for detailed inspection

## 🆘 Troubleshooting

### Diagram Not Rendering
- Check for syntax errors in the Mermaid code
- Ensure proper indentation
- Verify all quotes and brackets are balanced

### Performance Issues
- Large diagrams may be slow to render
- Try viewing in Mermaid Live Editor first
- Consider breaking complex diagrams into smaller parts

### Mobile Viewing Issues
- Use landscape orientation for better viewing
- Zoom in/out as needed
- Consider exporting as images for mobile sharing

This setup guide should help you view and work with the Mulberri system architecture diagrams across different platforms and use cases.
