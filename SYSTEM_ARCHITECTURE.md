# Mulberri Decision Tree System Architecture

## Overview

Mulberri is a production-grade decision tree implementation written in Go, designed for efficient offline training and fast real-time inference. The system follows a modular, interface-based architecture with clear separation of concerns and comprehensive error handling.

## System Architecture

### High-Level Architecture

The system is organized into several key layers:

1. **CLI Layer** (`cmd/mulberri/cli`) - Command-line interface and argument parsing
2. **Orchestration Layer** (`cmd/mulberri/main.go`) - Workflow coordination and command routing
3. **Core Packages** (`pkg/`) - Fundamental data structures and utilities
4. **Internal Packages** (`internals/`) - Business logic and algorithms
5. **Utilities** (`internals/utils`) - Data loading, parsing, and I/O operations

### Package Structure

```
mulberri/
├── cmd/mulberri/           # CLI application entry point
│   ├── cli/               # Command-line interface
│   ├── main.go            # Main orchestrator
│   └── serialization.go   # Model persistence
├── pkg/                   # Public API packages
│   ├── models/            # Core data structures
│   ├── logger/            # Structured logging
│   └── validation/        # Input validation
├── internals/             # Internal implementation
│   ├── core/              # Core algorithms
│   ├── training/          # Training pipeline
│   ├── prediction/        # Prediction pipeline
│   └── utils/             # Utilities and I/O
└── tests/                 # Test suites
```

## Core Components

### 1. Data Structures (`pkg/models`)

#### DecisionTree
The main model container that holds:
- Root TreeNode
- Feature definitions
- Tree statistics (depth, node count, leaf count)
- Target column information

#### TreeNode
Represents nodes in the decision tree:
- **Decision Nodes**: Internal nodes with splitting criteria
- **Leaf Nodes**: Terminal nodes with predictions and class distributions

#### FeatureStats
Comprehensive feature representation:
- Feature name and type (numeric/categorical)
- Column number in dataset
- Value ranges and categorical values
- Statistical information

### 2. Training Pipeline (`internals/training`)

#### C45 Splitter
Implements the C4.5 algorithm for finding optimal splits:
- Information gain and gain ratio calculation
- Handles both numeric and categorical features
- Configurable impurity criteria (entropy, gini, MSE)

#### Tree Builder (`internals/training/builder`)
Orchestrates tree construction:
- Interface-based design for modularity
- Recursive tree building with context cancellation
- Configurable stopping criteria
- Comprehensive validation and error handling

#### Feature Creation
Automatic feature type detection and creation:
- Analyzes raw CSV data to determine feature types
- Supports feature info files for explicit type specification
- Handles missing values and data validation

### 3. Prediction Pipeline (`internals/prediction`)

#### Tree Traversal
Efficient prediction engine:
- Multiple traversal strategies (recursive, iterative, automatic)
- Missing value handling with configurable strategies
- Decision path tracking for interpretability
- Batch processing support

#### Data Validation
Input validation for prediction:
- Feature compatibility checking
- Missing value detection
- Type validation

### 4. Utilities (`internals/utils`)

#### CSV Processing
Robust CSV data handling:
- RFC 4180 compliant parsing
- Feature info YAML parsing
- Prediction data loading
- Result formatting and export

#### Dataset Implementation
Memory-efficient dataset interface:
- Zero-copy subsetting
- Type conversion for numeric features
- Index-based views for subset operations

## Data Flow

### Training Workflow

1. **Input Processing**
   - Load CSV training data
   - Parse optional feature info YAML
   - Validate input data structure

2. **Feature Engineering**
   - Create FeatureStats objects from raw data
   - Automatic type detection or metadata-driven creation
   - Feature validation and range calculation

3. **Dataset Creation**
   - Wrap processed data in Dataset interface
   - Enable efficient access patterns for training

4. **Tree Construction**
   - Initialize C45 splitter with configuration
   - Create tree builder with stopping criteria
   - Recursively build decision tree using C4.5 algorithm

5. **Model Persistence**
   - Serialize trained model to JSON
   - Include feature definitions and tree structure

### Prediction Workflow

1. **Model Loading**
   - Deserialize JSON model file
   - Validate model structure and integrity

2. **Data Preparation**
   - Load prediction CSV data
   - Validate feature compatibility with model
   - Convert to internal prediction record format

3. **Prediction Generation**
   - Traverse decision tree for each record
   - Calculate confidence scores and probabilities
   - Track decision paths for interpretability

4. **Result Export**
   - Format prediction results
   - Export to CSV with configurable output options

## Key Interfaces

### Dataset[T comparable]
```go
type Dataset[T comparable] interface {
    GetSize() int
    GetFeatureValue(sampleIdx int, feature *models.FeatureStats) (interface{}, error)
    GetTarget(sampleIdx int) (T, error)
    GetIndices() []int
    Subset(indices []int) Dataset[T]
}
```

### Splitter[T comparable]
```go
type Splitter[T comparable] interface {
    FindBestSplit(ctx context.Context, dataset Dataset[T], features []*models.FeatureStats) (*SplitResult[T], error)
    CalculateImpurity(dataset Dataset[T]) (float64, error)
}
```

### TreeBuilder[T comparable]
```go
type TreeBuilder[T comparable] interface {
    BuildTree(ctx context.Context, dataset Dataset[T], features []*models.FeatureStats) (*models.DecisionTree, error)
}
```

## Design Principles

### 1. Interface-Based Design
- Pluggable components for extensibility
- Clear contracts between layers
- Testability through dependency injection

### 2. Type Safety
- Generic interfaces for type-safe operations
- Comprehensive input validation
- Structured error types with context

### 3. Performance Optimization
- Memory-efficient data structures
- Zero-copy operations where possible
- Configurable algorithms for different use cases

### 4. Production Readiness
- Graceful shutdown with context cancellation
- Comprehensive logging and error handling
- Configurable parameters for different environments

### 5. Maintainability
- Clear separation of concerns
- Extensive documentation and examples
- Comprehensive test coverage

## Configuration and Extensibility

The system supports extensive configuration through:
- Command-line flags for common parameters
- Feature info YAML files for metadata specification
- Functional options pattern for advanced configuration
- Environment-specific parameter tuning

## Error Handling

Structured error handling throughout:
- Custom error types with context information
- Graceful degradation for non-critical failures
- Clear error messages for debugging
- Validation at all input boundaries

## Performance Characteristics

- **Training**: Optimized for offline batch processing
- **Prediction**: Designed for real-time inference
- **Memory**: Efficient data structures with minimal copying
- **Scalability**: Handles large datasets with configurable memory usage

This architecture provides a robust foundation for decision tree machine learning applications while maintaining flexibility for future enhancements and optimizations.

## Detailed Component Analysis

### CLI Layer (`cmd/mulberri/cli`)

The CLI layer provides a clean interface for user interaction:

**Key Components:**
- `flags.go`: Command-line argument parsing with comprehensive validation
- `config.go`: Configuration structure and validation logic
- Support for both training and prediction workflows
- Built-in help system and error reporting

**Features:**
- Functional options pattern for configuration
- Structured error types with context
- Input validation at the CLI boundary
- Support for feature info files and advanced parameters

### Main Orchestration (`cmd/mulberri/main.go`)

The main orchestrator coordinates the entire workflow:

**Responsibilities:**
- Command routing (train/predict/version)
- Graceful shutdown with signal handling
- Progress reporting for long-running operations
- Error handling and user feedback
- Model serialization/deserialization

**Design Patterns:**
- Single responsibility principle
- Delegation to specialized packages
- Context-based cancellation
- Structured logging throughout

### Training Pipeline Deep Dive

#### Feature Creation Process
1. **Data Analysis**: Automatic detection of feature types from CSV data
2. **Metadata Integration**: Optional YAML feature info for explicit type specification
3. **Validation**: Comprehensive input validation and error reporting
4. **Optimization**: Memory-efficient processing with pre-allocation

#### C4.5 Algorithm Implementation
- **Information Gain**: Entropy-based split evaluation
- **Gain Ratio**: Bias correction for multi-valued attributes
- **Pruning**: Configurable stopping criteria
- **Missing Values**: Robust handling of incomplete data

#### Tree Building Strategy
- **Recursive Construction**: Depth-first tree building
- **Context Cancellation**: Graceful interruption support
- **Memory Management**: Efficient node allocation
- **Statistics Tracking**: Real-time tree metrics

### Prediction Pipeline Deep Dive

#### Tree Traversal Strategies
1. **Recursive Traversal**: Standard depth-first approach
2. **Iterative Traversal**: Stack-based for deep trees
3. **Automatic Selection**: Strategy selection based on tree characteristics

#### Confidence Calculation
- **Class Distribution**: Leaf node probability analysis
- **Path Confidence**: Decision path reliability scoring
- **Missing Value Handling**: Multiple strategies for incomplete data

#### Result Generation
- **Prediction Values**: Primary classification/regression output
- **Probability Distributions**: Full class probability vectors
- **Decision Paths**: Human-readable rule explanations
- **Confidence Scores**: Prediction reliability metrics

## Performance Characteristics

### Training Performance
- **Time Complexity**: O(n * m * log(n)) where n=samples, m=features
- **Space Complexity**: O(n + tree_size)
- **Optimization**: Parallel feature evaluation, memory pooling
- **Scalability**: Handles datasets with millions of samples

### Prediction Performance
- **Time Complexity**: O(log(tree_depth)) per prediction
- **Space Complexity**: O(1) per prediction
- **Throughput**: Thousands of predictions per second
- **Latency**: Sub-millisecond prediction times

### Memory Management
- **Zero-Copy Operations**: Dataset subsetting without duplication
- **Object Pooling**: Reuse of temporary objects
- **Garbage Collection**: Minimal allocation during prediction
- **Memory Mapping**: Efficient large file handling

## Extension Points

### Custom Splitters
Implement the `Splitter[T]` interface for custom splitting algorithms:
```go
type CustomSplitter[T comparable] struct {
    // Custom implementation
}

func (s *CustomSplitter[T]) FindBestSplit(ctx context.Context, dataset Dataset[T], features []*models.FeatureStats) (*SplitResult[T], error) {
    // Custom splitting logic
}
```

### Custom Traversal Strategies
Extend the prediction engine with custom traversal logic:
```go
type CustomTraversalStrategy struct {
    // Custom implementation
}

func (s *CustomTraversalStrategy) Traverse(tree *models.DecisionTree, record map[string]interface{}) (*TraversalResult, error) {
    // Custom traversal logic
}
```

### Custom Feature Types
Add support for new feature types by extending the feature creation system:
```go
type CustomFeatureType int

const (
    DateTimeFeature CustomFeatureType = iota
    GeospatialFeature
    TextFeature
)
```

## Testing Strategy

### Unit Testing
- **Component Isolation**: Each package has comprehensive unit tests
- **Mock Interfaces**: Dependency injection for testability
- **Edge Cases**: Boundary condition testing
- **Error Scenarios**: Comprehensive error path coverage

### Integration Testing
- **End-to-End Workflows**: Complete training and prediction pipelines
- **Data Validation**: Real-world dataset testing
- **Performance Benchmarks**: Regression testing for performance
- **Compatibility Testing**: Cross-platform validation

### Benchmark Testing
- **Performance Metrics**: Training and prediction speed benchmarks
- **Memory Profiling**: Memory usage analysis
- **Scalability Testing**: Large dataset performance validation
- **Regression Testing**: Performance regression detection

## Deployment Considerations

### Production Deployment
- **Container Support**: Docker containerization
- **Resource Management**: Configurable memory and CPU limits
- **Monitoring**: Structured logging and metrics
- **Health Checks**: Application health endpoints

### Scaling Strategies
- **Horizontal Scaling**: Multiple prediction service instances
- **Model Versioning**: Support for multiple model versions
- **Load Balancing**: Stateless prediction services
- **Caching**: Model and result caching strategies

This comprehensive architecture enables robust, scalable, and maintainable decision tree applications suitable for production environments.
